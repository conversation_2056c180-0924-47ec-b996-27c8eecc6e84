#!/usr/bin/env python3
"""
Verify that all environment variable issues have been fixed
"""
import os
import re
from pathlib import Path

def check_config_template():
    """Check that the main config template has COMPONENTS and all variables"""
    print("1. Checking config-template.toml.j2...")
    
    template_path = Path("backend/app/core/deployment/terraform_templates/config-template.toml.j2")
    
    with open(template_path, 'r') as f:
        content = f.read()
    
    # Check for COMPONENTS variable
    if 'COMPONENTS=' in content:
        print("✅ COMPONENTS environment variable is present")
    else:
        print("❌ COMPONENTS environment variable is missing")
        return False
    
    # Check for all required variables
    required_vars = [
        'STATIC_PAGE_SUBDOMAIN',
        'MAXMIND_LICENSE_KEY', 
        'KOMODO_PASSKEY'
    ]
    
    for var in required_vars:
        if var in content:
            print(f"✅ {var} is present")
        else:
            print(f"❌ {var} is missing")
            return False
    
    return True

def check_provider_files():
    """Check that provider files have been updated"""
    print("\n2. Checking provider files...")
    
    providers_dir = Path("backend/app/core/deployment/providers")
    provider_files = ["awslightsail.py", "digitalocean.py", "gcp.py", "hetzner.py", "linode.py", "vultr.py"]
    
    all_good = True
    
    for filename in provider_files:
        filepath = providers_dir / filename
        if not filepath.exists():
            continue
            
        with open(filepath, 'r') as f:
            content = f.read()
        
        # Check for new package structure (either conditional logic or no old "Premium" references)
        if ('deployment.package in ["Pangolin+", "Pangolin+AI"]' in content or
            ('Premium' not in content and 'static_page_subdomain' in content)):
            print(f"✅ {filename} has updated package logic")
        else:
            print(f"❌ {filename} still uses old package logic")
            all_good = False
        
        # Check for new variables
        if 'static_page_subdomain' in content and 'maxmind_license_key' in content:
            print(f"✅ {filename} includes new environment variables")
        else:
            print(f"❌ {filename} missing new environment variables")
            all_good = False
    
    return all_good

def check_terraform_templates():
    """Check that terraform templates have been updated"""
    print("\n3. Checking terraform templates...")
    
    templates_dir = Path("backend/app/core/deployment/terraform_templates")
    main_templates = ["digitalocean_main.tf.j2", "awslightsail_main.tf.j2", "gcp_main.tf.j2"]
    
    all_good = True
    
    for filename in main_templates:
        filepath = templates_dir / filename
        if not filepath.exists():
            continue
            
        with open(filepath, 'r') as f:
            content = f.read()
        
        # Check for new package conditions
        if 'package == "Pangolin+AI"' in content:
            print(f"✅ {filename} has Pangolin+AI specific conditions")
        else:
            print(f"❌ {filename} missing Pangolin+AI conditions")
            all_good = False
        
        # Check for new variables
        if 'maxmind_license_key' in content and 'komodo_passkey' in content:
            print(f"✅ {filename} includes new variables")
        else:
            print(f"❌ {filename} missing new variables")
            all_good = False
    
    return all_good

def check_package_configs():
    """Check that package configurations are correct"""
    print("\n4. Checking package configurations...")
    
    config_dir = Path("backend/config/packages")
    
    # Check Pangolin+AI config
    pangolin_ai_path = config_dir / "pangolin+AI.json"
    if pangolin_ai_path.exists():
        import json
        with open(pangolin_ai_path, 'r') as f:
            config = json.load(f)
        
        # Get all required env vars
        all_env_vars = set()
        for detail in config.get('details', []):
            all_env_vars.update(detail.get('required_env', []))
        
        expected_vars = {
            'DOMAIN', 'EMAIL', 'ADMIN_SUBDOMAIN', 'ADMIN_USERNAME', 'ADMIN_PASSWORD',
            'CROWDSEC_ENROLLMENT_KEY', 'KOMODO_HOST_IP', 'KOMODO_PASSKEY',
            'CLIENT_ID', 'CLIENT_SECRET', 'OPENAI_API_KEY', 
            'STATIC_PAGE_SUBDOMAIN', 'MAXMIND_LICENSE_KEY'
        }
        
        if expected_vars == all_env_vars:
            print("✅ Pangolin+AI package config has all required environment variables")
            return True
        else:
            missing = expected_vars - all_env_vars
            extra = all_env_vars - expected_vars
            if missing:
                print(f"❌ Pangolin+AI config missing: {missing}")
            if extra:
                print(f"⚠️  Pangolin+AI config has extra: {extra}")
            return False
    else:
        print("❌ Pangolin+AI config file not found")
        return False

def main():
    """Run all verification checks"""
    print("🔍 Verifying Environment Variables Fix\n")
    
    checks = [
        check_config_template,
        check_provider_files,
        check_terraform_templates,
        check_package_configs
    ]
    
    results = []
    for check in checks:
        results.append(check())
    
    print(f"\n📊 Verification Results:")
    check_names = [
        "Config Template",
        "Provider Files", 
        "Terraform Templates",
        "Package Configurations"
    ]
    
    for i, (name, result) in enumerate(zip(check_names, results)):
        print(f"{name}: {'✅ PASS' if result else '❌ FAIL'}")
    
    if all(results):
        print("\n🎉 All checks passed!")
        print("The environment variable issues have been fixed.")
        print("New deployments should now include all required environment variables.")
        return True
    else:
        print(f"\n❌ {len([r for r in results if not r])} checks failed.")
        print("Please review the issues above.")
        return False

if __name__ == "__main__":
    main()
