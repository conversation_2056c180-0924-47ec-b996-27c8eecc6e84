I want you to make changes to the frontend and possibly backend and the pricing in backend/config/pricing.csv.

First let me give you some context. I have changed the downstream service that provisions the deployments by cleaning up the environment variables that the app sends to it.

I want to have 3 packages now. Pangolin, Pangolin+ (was premium), Pangolin+AI

these packages should all have pricing entries so the pricing can be worked out. The Pricing should already be there and should be easy to change. Currently there are 2
columns package_multiplier_pangolin,package_multiplier_premium which show how much the price changes based on what package is selected. So this can simple change to handle 3 packages
package_multiplier_pangolin,package_multiplier_pangolin+,package_multiplier_pangolin+AI. Make the package_multiplier_pangolin+AI a multiplier of 2 (we can change that later)

The 2 different packages will require the following environment variables to be set in the files that define the deployment. THere are many such files in the backend/app/core/deployment/terraform_templates/ folder and this is an example of one backend/app/core/deployment/terraform_templates/awslightsail_config-template.toml.j2 - you can see the environment variables set (in upper case) like this:
[stack.config]
server = "server-{{ client_name_lower }}"
repo = "{{ github_repo }}"
reclone = true
file_paths = ["docker-compose-setup.yml"]
environment = """
DOMAIN={{ domain }}
EMAIL={{ admin_email }}
ADMIN_USERNAME={{ admin_username }}
ADMIN_PASSWORD={{ admin_password }}
ADMIN_SUBDOMAIN={{ admin_subdomain }}
POSTGRES_USER={{ postgres_user }}
POSTGRES_PASSWORD={{ postgres_password }}
POSTGRES_HOST={{ postgres_host }}
{% if package == "Premium" %}
CROWDSEC_ENROLLMENT_KEY={{ crowdsec_enrollment_key }}
STATIC_PAGE_DOMAIN={{ static_page_domain }}
CLIENT_ID={{ oauth_client_id }}
CLIENT_SECRET={{ oauth_client_secret }}
KOMODO_HOST_IP="{{ komodo_host_ip }}"
OPENAI_API_KEY={{ openai_api_key }}
{% endif %}
"""

So you will notice that there is a package variable. You will have to use this and make sure the values in the app are set as  Pangolin, Pangolin+ (was premium), Pangolin+AI

You will need to also update the UI in the frontend/src/pages/CreateDeployment.tsx file so it shows 3 package options.

Here are the definitions and fields required for the 3 packages. I am going to define them in json so that the front end can dynamically read from this json and can reflect the UI that matches. So if I change this JSON then the UI will change accordingly. Unless stated otherwise all the required_env are to be displayed a input fields but if the required_env contains the word password the UI field should be a password

here is an example of a structure for the pangolin+AI package
{
  "package": "pangolin+AI",
  "package_description": "Preconfigured Tunnelled reverse proxy with MCP and AI",
  "components": [
    "pangolin+",
    "crowdsec",
    "middleware-manager",
    "komodo",
    "mcpauth",
    "nlweb",
    "static-page",
    "traefik-log-dashboard"
  ],
  "details": [
    {
      "name": "pangolin+",
      "required_env": [
        "DOMAIN",
        "EMAIL",
        "ADMIN_SUBDOMAIN",
        "ADMIN_USERNAME",
        "ADMIN_PASSWORD"
      ],
      "description": "Preconfigured tunnelled reverse proxy"
    },
    {
      "name": "crowdsec",
      "required_env": [
        "CROWDSEC_ENROLLMENT_KEY"
      ],
      "description": "Crowd sourced security protection"
    },
    {
      "name": "komodo",
      "required_env": [
        "KOMODO_HOST_IP",
        "KOMODO_PASSKEY"
      ],
      "description": "Automated Deployments with Server/Container management"
    },
    {
      "name": "mcpauth",
      "required_env": [
        "CLIENT_ID",
        "CLIENT_SECRET"
      ],
      "description": "Provides Oauth for MCP server"
    },
    {
      "name": "middleware-manager",
      "required_env": [],
      "description": "A service that allows you to add custom middleware to Pangolin / Traefik resources"
    },
    {
      "name": "nlweb",
      "required_env": [
        "OPENAI_API_KEY"
      ],
      "description": "Natural Language Web"
    },
    {
      "name": "static-page",
      "required_env": [
        "STATIC_PAGE_SUBDOMAIN"
      ],
      "description": "Creates a static landing page and configures Traefik routing for it"
    },
    {
      "name": "traefik-log-dashboard",
      "required_env": [
        "MAXMIND_LICENSE_KEY"
      ],
      "description": "Enhanced Traefik log dashboard with OTLP support and GeoIP capabilities"
    }
  ]
}

this should result in a deployment config toml like this:


COMPONENTS="pangolin,crowdsec,middleware-manager,komodo,mcpauth,nlweb,static-page,traefik-log-dashboard"
DOMAIN=mcpgateway.online
EMAIL=<EMAIL>
ADMIN_USERNAME=<EMAIL>
ADMIN_PASSWORD=Mcpgateway123q!
ADMIN_SUBDOMAIN=pangolin
POSTGRES_USER=admin
POSTGRES_PASSWORD=dDuScoEE53vA2Q==
POSTGRES_HOST=komodo-postgres-1
CROWDSEC_ENROLLMENT_KEY=cm9vtmyk3000pjx08brfsa6wd
STATIC_PAGE_SUBDOMAIN=www
CLIENT_ID=************-jcil5rr35kloln6vdndph2iaupe890qd.apps.googleusercontent.com
CLIENT_SECRET=SECRETHERE
OPENAI_API_KEY="APIKEYHERE"
MAXMIND_LICENSE_KEY=****************************************


if however the user selects the pangolin+ package like this

{
  "package": "pangolin+",
  "package_description": "Preconfigured Tunnelled reverse proxy with middleware and logs",
  "components": [
    "pangolin+",
    "crowdsec",
    "middleware-manager",
    "static-page",
    "traefik-log-dashboard"
  ],
  "details": [
    {
      "name": "pangolin+",
      "required_env": [
        "DOMAIN",
        "EMAIL",
        "ADMIN_SUBDOMAIN",
        "ADMIN_USERNAME",
        "ADMIN_PASSWORD"
      ],
      "description": "Preconfigured tunnelled reverse proxy"
    },
    {
      "name": "crowdsec",
      "required_env": [
        "CROWDSEC_ENROLLMENT_KEY"
      ],
      "description": "Crowd sourced security protection"
    },
    {
      "name": "middleware-manager",
      "required_env": [],
      "description": "A service that allows you to add custom middleware to Pangolin / Traefik resources"
    },
    {
      "name": "static-page",
      "required_env": [
        "STATIC_PAGE_SUBDOMAIN"
      ],
      "description": "Creates a static landing page and configures Traefik routing for it"
    },
    {
      "name": "traefik-log-dashboard",
      "required_env": [
        "MAXMIND_LICENSE_KEY"
      ],
      "description": "Enhanced Traefik log dashboard with OTLP support and GeoIP capabilities"
    }
  ]
}

this should result in a deployment config toml like this:

COMPONENTS="pangolin,crowdsec,middleware-manager,static-page,traefik-log-dashboard"
DOMAIN=mcpgateway.online
EMAIL=<EMAIL>
ADMIN_USERNAME=<EMAIL>
ADMIN_PASSWORD=Mcpgateway123q!
ADMIN_SUBDOMAIN=pangolin
POSTGRES_USER=admin
POSTGRES_PASSWORD=dDuScoEE53vA2Q==
POSTGRES_HOST=komodo-postgres-1
CROWDSEC_ENROLLMENT_KEY=cm9vtmyk3000pjx08brfsa6wd
STATIC_PAGE_SUBDOMAIN=www
MAXMIND_LICENSE_KEY=****************************************


if however the user selects the standard pangolin package like this

{
  "package": "pangolin",
  "package_description": "Tunnelled reverse proxy",
  "components": [
    "pangolin"
  ],
  "details": [
    {
      "name": "pangolin",
      "required_env": [
        "DOMAIN",
        "EMAIL",
        "ADMIN_SUBDOMAIN"
      ],
      "description": "Tunnelled reverse proxy"
    }
  ]
}

this should result in a deployment config toml like this:

COMPONENTS="pangolin"
DOMAIN=mcpgateway.online
EMAIL=<EMAIL>
ADMIN_SUBDOMAIN=pangolin

By default the ADMIN_SUBDOMAIN=pangolin

You will notice that in the standard pangolin package we only ask for the  "DOMAIN","EMAIL","ADMIN_SUBDOMAIN". Specifically these are the domain that we are going to set up pangolin for, the email associated with your letsencypt account and the admin subdomain which is generally pangolin.yourdomain.com. In the other 2 packages we ask the user for an ADMIN username and password. THis is because we need to preconfigure the pangolin instance. 

You can test this by really configuring these services against the backend that is running on localhost:3000. The API Key (Bearer Token) is
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************.tGlBWmvSVdjrbiNxq-BwD3PIaEN23wu0C_PPlNcsWYw and you could use this to make curl requests to see what exactly is being sent over from the frontend to the backend.

ultimately when you create the deployments correctly - they get set up as terraform files in the folder backend/backend/deployments/

in your testing you could use a combination of curl requests and observation of the files that get generated in backend/backend/deployments to check that the system is working.

My guide to you is to try your best without asking me. Try not to change the code that is not related to the deployments and if possible reuse as much of the logic on how it works now. 