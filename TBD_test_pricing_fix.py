#!/usr/bin/env python3
"""
Test script to verify the pricing calculation fix
"""
import sys
import os
sys.path.append('backend')

from app.core.pricing.pricing_service import PricingService

def test_pricing_calculation():
    """Test pricing calculation for Pangolin+AI with Level 3 support"""
    print("🧪 Testing pricing calculation fix...\n")
    
    try:
        # Initialize pricing service
        pricing_service = PricingService()
        
        # Test case that was failing: Pangolin+AI with Level 3 support
        test_cases = [
            {
                "package": "Pangolin+AI",
                "cloud_provider": "AWS",
                "region": "us-east-1", 
                "instance_type": "t3.micro",
                "support_level": "Level 3"
            },
            {
                "package": "Pangolin+",
                "cloud_provider": "AWS",
                "region": "us-east-1",
                "instance_type": "t3.micro", 
                "support_level": "Level 3"
            },
            {
                "package": "Pangolin",
                "cloud_provider": "AWS",
                "region": "us-east-1",
                "instance_type": "t3.micro",
                "support_level": "Level 3"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"Test {i}: {test_case['package']} with {test_case['support_level']}")
            
            try:
                result = pricing_service.calculate_pricing(
                    cloud_provider=test_case["cloud_provider"],
                    region=test_case["region"],
                    instance_type=test_case["instance_type"],
                    package=test_case["package"],
                    support_level=test_case["support_level"]
                )
                
                print(f"✅ Success! Hourly cost: ${result['hourly_cost']:.4f}")
                print(f"   Package multiplier applied: {test_case['package']}")
                print(f"   Support cost included: {test_case['support_level']}")
                print(f"   Breakdown: {result['breakdown']}")
                
            except Exception as e:
                print(f"❌ Failed: {e}")
                return False
            
            print()
        
        print("🎉 All pricing calculations successful!")
        return True
        
    except Exception as e:
        print(f"❌ Error initializing pricing service: {e}")
        return False

if __name__ == "__main__":
    test_pricing_calculation()
