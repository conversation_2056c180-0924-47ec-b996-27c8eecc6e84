#!/usr/bin/env python3
"""
Final comprehensive test of the 3-package implementation
"""
import json
import csv
from pathlib import Path

def test_complete_implementation():
    """Test the complete 3-package implementation"""
    print("🧪 Final Implementation Test - 3-Package System\n")
    
    tests_passed = 0
    total_tests = 0
    
    # Test 1: Pricing CSV Structure
    print("1. Testing pricing CSV structure...")
    total_tests += 1
    try:
        csv_path = Path("backend/config/pricing.csv")
        with open(csv_path, 'r') as f:
            reader = csv.DictReader(f)
            header = reader.fieldnames
            
            required_columns = [
                'package_multiplier_pangolin',
                'package_multiplier_pangolin+', 
                'package_multiplier_pangolin+AI'
            ]
            
            if all(col in header for col in required_columns):
                # Check multiplier values
                first_row = next(reader)
                if (first_row['package_multiplier_pangolin'] == '1.0' and
                    first_row['package_multiplier_pangolin+'] == '1.5' and
                    first_row['package_multiplier_pangolin+AI'] == '2.0'):
                    print("✅ Pricing CSV has correct structure and multipliers")
                    tests_passed += 1
                else:
                    print("❌ Pricing CSV has incorrect multiplier values")
            else:
                print("❌ Pricing CSV missing required columns")
    except Exception as e:
        print(f"❌ Error testing pricing CSV: {e}")
    
    # Test 2: Package Configurations
    print("\n2. Testing package configurations...")
    total_tests += 1
    try:
        config_dir = Path("backend/config/packages")
        expected_packages = {
            "pangolin.json": ["DOMAIN", "EMAIL", "ADMIN_SUBDOMAIN"],
            "pangolin+.json": [
                "DOMAIN", "EMAIL", "ADMIN_SUBDOMAIN", "ADMIN_USERNAME", 
                "ADMIN_PASSWORD", "CROWDSEC_ENROLLMENT_KEY", 
                "STATIC_PAGE_SUBDOMAIN", "MAXMIND_LICENSE_KEY"
            ],
            "pangolin+AI.json": [
                "DOMAIN", "EMAIL", "ADMIN_SUBDOMAIN", "ADMIN_USERNAME", 
                "ADMIN_PASSWORD", "CROWDSEC_ENROLLMENT_KEY", 
                "KOMODO_HOST_IP", "KOMODO_PASSKEY", "CLIENT_ID", 
                "CLIENT_SECRET", "OPENAI_API_KEY", "STATIC_PAGE_SUBDOMAIN", 
                "MAXMIND_LICENSE_KEY"
            ]
        }
        
        all_configs_correct = True
        for filename, expected_vars in expected_packages.items():
            filepath = config_dir / filename
            if filepath.exists():
                with open(filepath, 'r') as f:
                    config = json.load(f)
                
                actual_vars = set()
                for detail in config.get('details', []):
                    actual_vars.update(detail.get('required_env', []))
                
                if set(expected_vars) == actual_vars:
                    print(f"✅ {filename} has correct environment variables")
                else:
                    print(f"❌ {filename} has incorrect environment variables")
                    all_configs_correct = False
            else:
                print(f"❌ {filename} not found")
                all_configs_correct = False
        
        if all_configs_correct:
            tests_passed += 1
    except Exception as e:
        print(f"❌ Error testing package configurations: {e}")
    
    # Test 3: Backend API Endpoint
    print("\n3. Testing backend API endpoint...")
    total_tests += 1
    try:
        api_file = Path("backend/app/api/deployments.py")
        with open(api_file, 'r') as f:
            content = f.read()
        
        if ('"Pangolin+AI"' in content and
            'get_package_config' in content and
            '/packages/{package_name}/config' in content):
            print("✅ Backend API has correct package endpoints")
            tests_passed += 1
        else:
            print("❌ Backend API missing required updates")
    except Exception as e:
        print(f"❌ Error testing backend API: {e}")
    
    # Test 4: Frontend Types
    print("\n4. Testing frontend types...")
    total_tests += 1
    try:
        types_file = Path("frontend/src/api/types.ts")
        with open(types_file, 'r') as f:
            content = f.read()
        
        if ("'Pangolin+AI'" in content and 
            'interface PackageConfig' in content and
            'static_page_subdomain' in content and
            'komodo_passkey' in content and
            'maxmind_license_key' in content):
            print("✅ Frontend types have correct structure")
            tests_passed += 1
        else:
            print("❌ Frontend types missing required updates")
    except Exception as e:
        print(f"❌ Error testing frontend types: {e}")
    
    # Test 5: Deployment Templates
    print("\n5. Testing deployment templates...")
    total_tests += 1
    try:
        template_file = Path("backend/app/core/deployment/terraform_templates/config-template.toml.j2")
        with open(template_file, 'r') as f:
            content = f.read()
        
        if ('package in ["Pangolin+", "Pangolin+AI"]' in content and
            'package == "Pangolin+AI"' in content and
            'STATIC_PAGE_SUBDOMAIN' in content and
            'MAXMIND_LICENSE_KEY' in content and
            'KOMODO_PASSKEY' in content):
            print("✅ Deployment templates have correct conditional logic")
            tests_passed += 1
        else:
            print("❌ Deployment templates missing required updates")
    except Exception as e:
        print(f"❌ Error testing deployment templates: {e}")
    
    # Test 6: Frontend CreateDeployment Component
    print("\n6. Testing frontend CreateDeployment component...")
    total_tests += 1
    try:
        component_file = Path("frontend/src/pages/CreateDeployment.tsx")
        with open(component_file, 'r') as f:
            content = f.read()
        
        if ('PackageConfig' in content and
            'fetchPackageConfig' in content and
            'envVarToFieldName' in content and
            'isConfigCompleted' in content and
            'grid-cols-3' in content):
            print("✅ CreateDeployment component has dynamic configuration")
            tests_passed += 1
        else:
            print("❌ CreateDeployment component missing required updates")
    except Exception as e:
        print(f"❌ Error testing CreateDeployment component: {e}")
    
    # Summary
    print(f"\n📊 Final Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("\n🎉 IMPLEMENTATION COMPLETE!")
        print("✅ All components updated for 3-package system")
        print("✅ Environment variables correctly configured per package")
        print("✅ Frontend dynamically renders based on package selection")
        print("✅ Backend supports new package structure")
        print("✅ Deployment templates handle all package types")
        return True
    else:
        print(f"\n❌ {total_tests - tests_passed} tests failed. Please review implementation.")
        return False

if __name__ == "__main__":
    test_complete_implementation()
