#!/usr/bin/env python3
"""
Fix all remaining "Premium" package references in template files
"""
import os
import re
from pathlib import Path

def fix_template_file(filepath):
    """Fix a single template file"""
    print(f"Fixing {filepath}...")
    
    with open(filepath, 'r') as f:
        content = f.read()
    
    original_content = content
    
    # Fix 1: Update package conditions from "Premium" to new packages
    content = re.sub(
        r'{% if package == "Premium" %}',
        '{% if package in ["Pangolin+", "Pangolin+AI"] %}',
        content
    )
    
    # Fix 2: Replace static_page_domain with static_page_subdomain in terraform.tfvars files
    content = re.sub(
        r'static_page_domain\s*=',
        'static_page_subdomain =',
        content
    )
    
    # Fix 3: Add missing variables for Pangolin+AI in terraform.tfvars files
    if 'terraform.tfvars.j2' in str(filepath) and 'Premium' in original_content:
        # Replace the Premium section with the new structure
        premium_pattern = r'{% if package == "Premium" %}.*?{% endif %}'
        new_section = '''{% if package in ["Pangolin+", "Pangolin+AI"] %}
# Pangolin+ Package Configuration
crowdsec_enrollment_key = "{{ crowdsec_enrollment_key or '' }}"
static_page_subdomain   = "{{ static_page_subdomain or '' }}"
maxmind_license_key     = "{{ maxmind_license_key or '' }}"
{% endif %}

{% if package == "Pangolin+AI" %}
# Pangolin+AI Additional Configuration
oauth_client_id         = "{{ oauth_client_id or '' }}"
oauth_client_secret     = "{{ oauth_client_secret or '' }}"
komodo_host_ip          = "{{ komodo_host_ip or '' }}"
komodo_passkey          = "{{ komodo_passkey or '' }}"
openai_api_key          = "{{ openai_api_key or '' }}"
{% endif %}'''
        
        content = re.sub(premium_pattern, new_section, content, flags=re.DOTALL)
    
    # Fix 4: Update config template files that still have Premium references
    if 'config-template.toml.j2' in str(filepath) and 'Premium' in original_content:
        # Replace Premium conditions with new package structure
        content = re.sub(
            r'{% if package == "Premium" %}.*?{% endif %}',
            '''{% if package in ["Pangolin+", "Pangolin+AI"] %}
CROWDSEC_ENROLLMENT_KEY={{ crowdsec_enrollment_key }}
STATIC_PAGE_SUBDOMAIN={{ static_page_subdomain }}
MAXMIND_LICENSE_KEY={{ maxmind_license_key }}
{% endif %}
{% if package == "Pangolin+AI" %}
CLIENT_ID={{ oauth_client_id }}
CLIENT_SECRET={{ oauth_client_secret }}
KOMODO_HOST_IP={{ komodo_host_ip }}
KOMODO_PASSKEY={{ komodo_passkey }}
OPENAI_API_KEY={{ openai_api_key }}
{% endif %}''',
            content,
            flags=re.DOTALL
        )
    
    # Only write if content changed
    if content != original_content:
        with open(filepath, 'w') as f:
            f.write(content)
        print(f"✅ Fixed {filepath}")
    else:
        print(f"⚪ No changes needed for {filepath}")

def main():
    """Fix all template files with Premium references"""
    print("🔧 Fixing remaining Premium package references...\n")
    
    templates_dir = Path(".")  # Current directory is already the templates dir
    
    # Find all template files that might have Premium references
    template_files = list(templates_dir.glob("*.j2"))
    
    for filepath in template_files:
        try:
            fix_template_file(filepath)
        except Exception as e:
            print(f"❌ Error fixing {filepath}: {e}")
    
    print("\n🎉 All Premium references have been updated!")
    print("Template files now use the new 3-package structure.")

if __name__ == "__main__":
    main()
