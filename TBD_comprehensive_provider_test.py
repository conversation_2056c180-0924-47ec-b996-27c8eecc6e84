#!/usr/bin/env python3
"""
Comprehensive test script for all cloud providers
"""
import json
import subprocess
import time
import requests
from pathlib import Path

# Bearer token
TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************.SQ1O5RQR_2IJcabqRSzHSWppNr5QxNAMy63kFQi7JKU"
BASE_URL = "http://localhost:3000/api/deployments/"

# Test configurations for each provider
PROVIDERS = [
    {
        "name": "Google Cloud",
        "region": "us-central1",
        "instance_type": "e2-micro"
    },
    {
        "name": "Azure", 
        "region": "eastus",
        "instance_type": "Standard_B2s"
    },
    {
        "name": "<PERSON><PERSON><PERSON>",
        "region": "nbg1",
        "instance_type": "cx11"
    },
    {
        "name": "Linode",
        "region": "us-east",
        "instance_type": "g6-nanode-1"
    },
    {
        "name": "AWS",
        "region": "us-east-1", 
        "instance_type": "t3.micro"
    }
]

def create_deployment(provider_config):
    """Create a deployment for the given provider"""
    payload = {
        "package": "Pangolin+AI",
        "server_type": "new",
        "cloud_provider": provider_config["name"],
        "region": provider_config["region"],
        "instance_type": provider_config["instance_type"],
        "support_level": "Level 2",
        "admin_subdomain": "pangolin",
        "domain": "contextware.ai",
        "admin_email": "<EMAIL>",
        "admin_username": "<EMAIL>",
        "admin_password": "Password123q!",
        "crowdsec_enrollment_key": "cm9vtmyk3000pjx08brfsa6wd",
        "komodo_host_ip": "*************",
        "komodo_passkey": "123456",
        "oauth_client_id": "255468043062-jcil5rr35kloln6vdndph2iaupe890qd.apps.googleusercontent.com",
        "oauth_client_secret": "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",
        "openai_api_key": "*********************************************************************************************************************************************** 8If1yvOXOLk2PYKTHE4kA",
        "static_page_subdomain": "www",
        "maxmind_license_key": "****************************************",
        "client_name": f"test-{provider_config['name'].lower().replace(' ', '-')}",
        "client_id": f"client_test-{provider_config['name'].lower().replace(' ', '-')}",
        "postgres_host": "komodo-postgres-1",
        "postgres_user": "admin",
        "postgres_password": "dDuScoEE53vA2Q==",
        "github_repo": "oidebrett/manidae"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {TOKEN}"
    }
    
    try:
        response = requests.post(BASE_URL, json=payload, headers=headers, timeout=30)
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ Failed to create deployment for {provider_config['name']}: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Error creating deployment for {provider_config['name']}: {e}")
        return None

def check_files(deployment_id, provider_name):
    """Check the generated files for a deployment"""
    deployment_dir = Path(f"backend/backend/deployments/{deployment_id}")
    
    if not deployment_dir.exists():
        return {"status": "❌", "error": "Deployment directory not found"}
    
    results = {}
    
    # Check terraform.tfvars
    tfvars_file = deployment_dir / "terraform.tfvars"
    if tfvars_file.exists():
        content = tfvars_file.read_text()
        required_vars = ["komodo_passkey", "maxmind_license_key", "openai_api_key", "static_page_subdomain"]
        missing_vars = [var for var in required_vars if var not in content]
        
        if missing_vars:
            results["terraform.tfvars"] = f"❌ Missing: {missing_vars}"
        else:
            results["terraform.tfvars"] = "✅ All variables present"
    else:
        results["terraform.tfvars"] = "❌ File not found"
    
    # Check config-template.toml
    config_file = deployment_dir / "config-template.toml"
    if config_file.exists():
        content = config_file.read_text()
        if "COMPONENTS=" in content:
            results["config-template.toml"] = "✅ COMPONENTS present"
        else:
            results["config-template.toml"] = "❌ COMPONENTS missing"
    else:
        results["config-template.toml"] = "❌ File not found"
    
    return results

def test_terraform_plan(deployment_id):
    """Test terraform plan for a deployment"""
    deployment_dir = Path(f"backend/backend/deployments/{deployment_id}")
    
    try:
        # Run terraform init
        result = subprocess.run(
            ["terraform", "init"],
            cwd=deployment_dir,
            capture_output=True,
            text=True,
            timeout=60
        )
        
        if result.returncode != 0:
            return f"❌ terraform init failed: {result.stderr[:100]}"
        
        # Run terraform plan
        result = subprocess.run(
            ["terraform", "plan", "-input=false"],
            cwd=deployment_dir,
            capture_output=True,
            text=True,
            timeout=60
        )
        
        if result.returncode == 0:
            return "✅ terraform plan successful"
        else:
            return f"❌ terraform plan failed: {result.stderr[:100]}"
            
    except Exception as e:
        return f"❌ terraform test error: {str(e)[:100]}"

def cleanup_deployment(deployment_id):
    """Delete a deployment"""
    headers = {"Authorization": f"Bearer {TOKEN}"}
    
    try:
        response = requests.delete(f"{BASE_URL}{deployment_id}", headers=headers, timeout=10)
        subprocess.run(["rm", "-rf", f"backend/backend/deployments/{deployment_id}"], check=False)
        return response.status_code == 200
    except:
        return False

def main():
    """Run comprehensive provider tests"""
    print("🧪 COMPREHENSIVE CLOUD PROVIDER TESTING\n")
    
    results = {}
    
    for provider in PROVIDERS:
        provider_name = provider["name"]
        print(f"Testing {provider_name}...")
        
        # Create deployment
        deployment = create_deployment(provider)
        if not deployment:
            results[provider_name] = {"status": "❌", "error": "Failed to create deployment"}
            continue
        
        deployment_id = deployment.get("id")
        if not deployment_id:
            results[provider_name] = {"status": "❌", "error": "No deployment ID returned"}
            continue
        
        # Wait a moment for files to be generated
        time.sleep(2)
        
        # Check files
        file_results = check_files(deployment_id, provider_name)
        
        # Test terraform
        terraform_result = test_terraform_plan(deployment_id)
        
        # Store results
        results[provider_name] = {
            "deployment_id": deployment_id,
            "files": file_results,
            "terraform": terraform_result,
            "status": "✅" if "✅" in str(file_results) and "✅" in terraform_result else "❌"
        }
        
        # Cleanup
        cleanup_deployment(deployment_id)
        
        print(f"  {results[provider_name]['status']} {provider_name} completed")
    
    # Print final report
    print("\n📊 FINAL TEST RESULTS:")
    print("=" * 80)
    
    for provider_name, result in results.items():
        print(f"\n{result['status']} {provider_name}")
        if "files" in result:
            for file_type, file_result in result["files"].items():
                print(f"  {file_result} - {file_type}")
        if "terraform" in result:
            print(f"  {result['terraform']} - terraform plan")
    
    # Summary
    success_count = sum(1 for r in results.values() if r.get("status") == "✅")
    total_count = len(results)
    
    print(f"\n🎯 SUMMARY: {success_count}/{total_count} providers passed all tests")
    
    if success_count == total_count:
        print("🎉 ALL PROVIDERS WORKING CORRECTLY!")
    else:
        failed = [name for name, r in results.items() if r.get("status") == "❌"]
        print(f"❌ Failed providers: {', '.join(failed)}")

if __name__ == "__main__":
    main()
