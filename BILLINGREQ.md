**Objective** 
Enhance the existing cloud deployment web application to include **billing, balance enforcement, Stripe payments, low-balance notifications, and automatic deployment termination**, according to the specifications below. 
 
--- 
 
### **1. Preconditions & Context** 
 
* **Tech stack**: The app is already functional with user accounts, deployments, and the `Deployment` model (see below). 
* **Current DB schema** for deployments: 
 
```python 
class Deployment(DeploymentBase): 
    id: int 
    user_id: int | None = None 
    cost: int  # hourly cost 
    deleted_at: datetime | None = None 
    created_at: datetime 
    status: str 
 
    class Config: 
        from_attributes = True 
``` 
 
* **Existing features**: 
 
  * Users can create and destroy deployments via the web app. 
  * Admins can also delete deployments. 
  * `cost` is the hourly cost per deployment. 
  * The smallest billing unit is 1 hour (partial hours round up). 
* **Separate script** exists for bandwidth overage monitoring, which can integrate later with the billing system. 
 
--- 
 
### **2. New Requirements** 
 
#### **A. Balance Management** 
 
1. Add a **`User.balance`** field (in EUR) to store the current account balance. 
2. Add an **application config value** (e.g. `MIN_BALANCE_TO_DEPLOY`) that defaults to **5 EUR** but is configurable. 
3. Prevent deployment creation if `user.balance < MIN_BALANCE_TO_DEPLOY`. 
4. Provide a **Stripe integration** for users to add funds to their account. 
 
   * Implement using Stripe Checkout or Payment Intents. 
   * Ensure webhook handling to update `User.balance` upon successful payment. 
5. Allow **admin users** to directly adjust (`add` or `subtract`) a user's balance without Stripe. 
 
   * Create an API endpoint and admin UI control for this. 
 
--- 
 
#### **B. Hourly Billing Enforcement** 
 
1. Add a **configurable scheduled task** (default: runs every hour) that: 
 
   * Finds all **active deployments** (`deleted_at IS NULL`). 
   * Calculates billable hours since the **last billing checkpoint** for each deployment (round up partial hours). 
   * Deducts `(hours_elapsed × deployment.cost)` from the corresponding user's balance. 
2. If **user.balance < MIN\_BALANCE\_TO\_DEPLOY** (but > 0), send a **low-balance warning**: 
 
   * For now, just store/display a dashboard message or simulate email sending. 
3. If **user.balance <= 0**, terminate deployments in **descending cost order** until the balance is ≥ 0 or no deployments remain: 
 
   * Mark `deleted_at` in DB. 
   * Trigger existing cloud termination logic. 
4. Record all balance changes in a **`Transaction` table** with: 
 
   * `id`, `user_id`, `amount`, `type` (`billing`, `manual_adjustment`, `payment`), `description`, `created_at`. 
 
--- 
 
#### **C. Notifications** 
 
1. Implement placeholder notification logic: 
 
   * When low balance: display message in dashboard and log warning to console. 
   * When balance hits zero: display urgent message and log shutdown events. 
2. Later: Replace placeholder with SMTP-based email sending. 
 
--- 
 
#### **D. FAQ Page** 
 
1. Add a **FAQ section** in the web app explaining: 
 
   * How hourly billing works (full hours billed even for partial use). 
   * Minimum balance rules. 
   * Low balance and zero balance consequences. 
   * Bandwidth overage policy (state that exceeding the limit results in deletion). 
2. Make the bandwidth limit a configurable value. 
 
--- 
 
### **3. Implementation Details** 
 
#### **Data Model Changes** 
 
* `User` model: Add `balance: Decimal` (precision to 2 decimal places). 
* New table: `Transaction` for tracking all balance changes. 
* Optional: Add `last_billed_at` to `Deployment` for more precise hourly billing. 
 
--- 
 
#### **APIs & Endpoints** 
 
* **POST /stripe/create-checkout-session** → returns Stripe session URL. 
* **POST /stripe/webhook** → updates `User.balance` after payment success. 
* **POST /admin/users/{id}/adjust-balance** → admin-only. 
* **GET /transactions** → user’s own transaction history. 
 
--- 
 
#### **Background Jobs** 
 
* Use existing scheduling system (Celery, APScheduler, or cron) to: 
 
  * Bill active deployments hourly. 
  * Trigger low-balance notifications. 
  * Auto-delete deployments when balance ≤ 0. 
 
--- 
 
#### **Business Logic Notes** 
 
* Billing for partial hours = **ceil(minutes\_used / 60)**. 
* If multiple deployments are deleted for zero balance: 
 
  * Sort by `cost` descending, terminate until balance >= 0 or all are gone. 
* Store **every deduction** as a negative `Transaction`. 
 
--- 
 
#### **Testing Requirements** 
 
* Test that: 
 
  * User cannot create deployment if balance < `MIN_BALANCE_TO_DEPLOY`. 
  * Stripe payments increase balance. 
  * Admin adjustments update balance. 
  * Hourly billing runs and deducts balance correctly. 
  * Low balance notifications trigger at correct threshold. 
  * Zero balance deletions happen and in correct cost order. 
  * Transaction log is complete and accurate. 
 
--- 
 
### **4. Optional Integration** 
 
Later, integrate bandwidth overage monitoring script so that if a user exceeds bandwidth, the same deletion & notification logic applies. 
 
Try to make me  Python cron job implementation** that matches the requirements you described. 
 
This should: 
 
* Run every hour (or at a configurable interval) 
* Deduct hourly costs from user balances for active deployments 
* Trigger low-balance warnings 
* Terminate deployments when balance hits zero 
* Log all changes in a `Transaction` table 
