#!/usr/bin/env python3
"""
Test script to verify the 3-package implementation
"""
import json
import csv
import os
from pathlib import Path

def test_pricing_csv():
    """Test that pricing.csv has been updated correctly"""
    print("Testing pricing.csv...")
    
    csv_path = Path("backend/config/pricing.csv")
    if not csv_path.exists():
        print("❌ pricing.csv not found")
        return False
    
    with open(csv_path, 'r') as f:
        reader = csv.DictReader(f)
        header = reader.fieldnames
        
        # Check header has new columns
        expected_columns = [
            'package_multiplier_pangolin',
            'package_multiplier_pangolin+', 
            'package_multiplier_pangolin+AI'
        ]
        
        for col in expected_columns:
            if col not in header:
                print(f"❌ Missing column: {col}")
                return False
        
        # Check first row has correct multipliers
        first_row = next(reader)
        if first_row['package_multiplier_pangolin+AI'] != '2.0':
            print(f"❌ Pangolin+AI multiplier should be 2.0, got {first_row['package_multiplier_pangolin+AI']}")
            return False
    
    print("✅ pricing.csv updated correctly")
    return True

def test_package_configs():
    """Test that package configuration files exist and are valid"""
    print("Testing package configuration files...")
    
    config_dir = Path("backend/config/packages")
    if not config_dir.exists():
        print("❌ Package config directory not found")
        return False
    
    expected_files = ["pangolin.json", "pangolin+.json", "pangolin+AI.json"]
    
    for filename in expected_files:
        filepath = config_dir / filename
        if not filepath.exists():
            print(f"❌ Missing config file: {filename}")
            return False
        
        try:
            with open(filepath, 'r') as f:
                config = json.load(f)
            
            # Validate structure
            required_keys = ['package', 'package_description', 'components', 'details']
            for key in required_keys:
                if key not in config:
                    print(f"❌ Missing key '{key}' in {filename}")
                    return False
            
            print(f"✅ {filename} is valid")
        except json.JSONDecodeError as e:
            print(f"❌ Invalid JSON in {filename}: {e}")
            return False
    
    return True

def test_api_endpoints():
    """Test that API endpoint code has been updated"""
    print("Testing API endpoints...")
    
    api_file = Path("backend/app/api/deployments.py")
    if not api_file.exists():
        print("❌ API file not found")
        return False
    
    with open(api_file, 'r') as f:
        content = f.read()
    
    # Check packages endpoint returns 3 packages
    if '"Pangolin+AI"' not in content:
        print("❌ Pangolin+AI not found in packages endpoint")
        return False
    
    # Check package config endpoint exists
    if 'get_package_config' not in content:
        print("❌ Package config endpoint not found")
        return False
    
    print("✅ API endpoints updated correctly")
    return True

def test_frontend_types():
    """Test that frontend types have been updated"""
    print("Testing frontend types...")
    
    types_file = Path("frontend/src/api/types.ts")
    if not types_file.exists():
        print("❌ Frontend types file not found")
        return False
    
    with open(types_file, 'r') as f:
        content = f.read()
    
    # Check package types include new packages
    if "'Pangolin+AI'" not in content:
        print("❌ Pangolin+AI type not found in frontend types")
        return False
    
    # Check PackageConfig interface exists
    if 'interface PackageConfig' not in content:
        print("❌ PackageConfig interface not found")
        return False
    
    print("✅ Frontend types updated correctly")
    return True

def test_deployment_templates():
    """Test that deployment templates have been updated"""
    print("Testing deployment templates...")
    
    template_file = Path("backend/app/core/deployment/terraform_templates/config-template.toml.j2")
    if not template_file.exists():
        print("❌ Template file not found")
        return False
    
    with open(template_file, 'r') as f:
        content = f.read()
    
    # Check new package conditions
    if 'package in ["Pangolin+", "Pangolin+AI"]' not in content:
        print("❌ New package conditions not found in template")
        return False
    
    if 'package == "Pangolin+AI"' not in content:
        print("❌ Pangolin+AI specific condition not found")
        return False
    
    print("✅ Deployment templates updated correctly")
    return True

def main():
    """Run all tests"""
    print("🧪 Testing 3-package implementation...\n")
    
    tests = [
        test_pricing_csv,
        test_package_configs,
        test_api_endpoints,
        test_frontend_types,
        test_deployment_templates
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The 3-package implementation is ready.")
        return True
    else:
        print("❌ Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    main()
