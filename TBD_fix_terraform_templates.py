#!/usr/bin/env python3
"""
Fix all terraform template files to include the missing environment variables
"""
import os
import re
from pathlib import Path

def fix_main_template(filepath):
    """Fix a main.tf template file"""
    print(f"Fixing {filepath}...")
    
    with open(filepath, 'r') as f:
        content = f.read()
    
    # Fix the template variables section
    # Look for the pattern where variables are passed to the config template
    pattern = r'({% if package in \["Pangolin\+", "Pangolin\+AI"\] %}.*?)({% endif %})'
    
    def replace_vars(match):
        return '''{% if package in ["Pangolin+", "Pangolin+AI"] %}
    crowdsec_enrollment_key = var.crowdsec_enrollment_key
    static_page_subdomain   = var.static_page_subdomain
    maxmind_license_key     = var.maxmind_license_key
    {% endif %}
    {% if package == "Pangolin+AI" %}
    oauth_client_id         = var.oauth_client_id
    oauth_client_secret     = var.oauth_client_secret
    komodo_passkey          = var.komodo_passkey
    openai_api_key          = var.openai_api_key'''
    
    # Replace the old pattern with the new one
    content = re.sub(pattern, replace_vars, content, flags=re.DOTALL)
    
    # Also fix any remaining static_page_domain references
    content = re.sub(r'static_page_domain', 'static_page_subdomain', content)
    
    with open(filepath, 'w') as f:
        f.write(content)
    
    print(f"✅ Fixed {filepath}")

def fix_variables_template(filepath):
    """Fix a variables.tf template file"""
    print(f"Fixing {filepath}...")
    
    with open(filepath, 'r') as f:
        content = f.read()
    
    # Make sure all new variables are included
    new_vars = [
        ('static_page_subdomain', 'Static page subdomain'),
        ('maxmind_license_key', 'MaxMind license key for GeoIP'),
        ('komodo_passkey', 'Komodo passkey for authentication')
    ]
    
    for var_name, description in new_vars:
        if f'variable "{var_name}"' not in content:
            # Add the variable if it's missing
            var_block = f'''
variable "{var_name}" {{
  description = "{description}"
  type        = string
  sensitive   = {"true" if "key" in var_name or "passkey" in var_name else "false"}
}}
'''
            # Insert before the last {% endif %}
            content = content.replace('{% endif %}', var_block + '{% endif %}')
    
    with open(filepath, 'w') as f:
        f.write(content)
    
    print(f"✅ Fixed {filepath}")

def main():
    """Fix all terraform template files"""
    print("🔧 Fixing all terraform template files...\n")
    
    templates_dir = Path("backend/app/core/deployment/terraform_templates")
    
    # Fix main.tf templates
    main_templates = [
        "awslightsail_main.tf.j2",
        "digitalocean_main.tf.j2",
        "gcp_main.tf.j2", 
        "hetzner_main.tf.j2",
        "linode_main.tf.j2",
        "vultr_main.tf.j2",
        "azure_main.tf.j2"
    ]
    
    for filename in main_templates:
        filepath = templates_dir / filename
        if filepath.exists():
            try:
                fix_main_template(filepath)
            except Exception as e:
                print(f"❌ Error fixing {filename}: {e}")
        else:
            print(f"⚠️  File not found: {filename}")
    
    # Fix variables.tf templates  
    variables_templates = [
        "awslightsail_variables.tf.j2",
        "digitalocean_variables.tf.j2",
        "gcp_variables.tf.j2",
        "hetzner_variables.tf.j2", 
        "linode_variables.tf.j2",
        "vultr_variables.tf.j2",
        "azure_variables.tf.j2"
    ]
    
    for filename in variables_templates:
        filepath = templates_dir / filename
        if filepath.exists():
            try:
                fix_variables_template(filepath)
            except Exception as e:
                print(f"❌ Error fixing {filename}: {e}")
        else:
            print(f"⚠️  File not found: {filename}")
    
    print("\n🎉 All terraform template files have been updated!")
    print("The missing environment variables should now be properly passed to deployments.")

if __name__ == "__main__":
    main()
