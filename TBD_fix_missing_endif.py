#!/usr/bin/env python3
"""
Fix missing {% endif %} tags in template files
"""
import os
import re
from pathlib import Path

def fix_template_file(filepath):
    """Fix missing endif tags in a template file"""
    print(f"Fixing {filepath}...")
    
    with open(filepath, 'r') as f:
        content = f.read()
    
    original_content = content
    
    # Pattern to find Pangolin+AI blocks that are missing {% endif %}
    # Look for the pattern where we have the variables but no closing endif before })
    pattern = r'({% if package == "Pangolin\+AI" %}.*?openai_api_key.*?)\n(\s*}\))'
    
    def add_endif(match):
        variables_block = match.group(1)
        closing_block = match.group(2)
        return f"{variables_block}\n    {{% endif %}}\n{closing_block}"
    
    content = re.sub(pattern, add_endif, content, flags=re.DOTALL)
    
    # Only write if content changed
    if content != original_content:
        with open(filepath, 'w') as f:
            f.write(content)
        print(f"✅ Fixed {filepath}")
        return True
    else:
        print(f"⚪ No changes needed for {filepath}")
        return False

def main():
    """Fix all template files with missing endif tags"""
    print("🔧 Fixing missing {% endif %} tags...\n")
    
    templates_dir = Path("terraform_templates")
    
    # Files that need fixing based on the check
    files_to_fix = [
        "gcp_main.tf.j2",
        "hetzner_main.tf.j2", 
        "digitalocean_main.tf.j2",
        "awslightsail_main.tf.j2"
    ]
    
    fixed_count = 0
    
    for filename in files_to_fix:
        filepath = templates_dir / filename
        if filepath.exists():
            try:
                if fix_template_file(filepath):
                    fixed_count += 1
            except Exception as e:
                print(f"❌ Error fixing {filename}: {e}")
        else:
            print(f"⚠️  File not found: {filename}")
    
    print(f"\n🎉 Fixed {fixed_count} template files!")
    print("All template files should now have balanced if/endif blocks.")

if __name__ == "__main__":
    main()
