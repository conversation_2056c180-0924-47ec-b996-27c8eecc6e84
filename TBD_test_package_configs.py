#!/usr/bin/env python3
"""
Test script to verify the package configurations are correct
"""
import json
from pathlib import Path

def test_package_configs():
    """Test that package configurations match the expected environment variables"""
    print("🧪 Testing package configurations...\n")
    
    config_dir = Path("backend/config/packages")
    
    # Expected environment variables for each package
    expected_env_vars = {
        "pangolin.json": {
            "DOMAIN", "EMAIL", "ADMIN_SUBDOMAIN"
        },
        "pangolin+.json": {
            "DOMAIN", "EMAIL", "ADMIN_SUBDOMAIN", "ADMIN_USERNAME", "ADMIN_PASSWORD",
            "CROWDSEC_ENROLLMENT_KEY", "STATIC_PAGE_SUBDOMAIN", "MAXMIND_LICENSE_KEY"
        },
        "pangolin+AI.json": {
            "DOMAIN", "EMAIL", "ADMIN_SUBDOMAIN", "ADMIN_USERNAME", "ADMIN_PASSWORD",
            "CROWDSEC_ENROLLMENT_KEY", "KOMODO_HOST_IP", "KOMODO_PASSKEY",
            "CLIENT_ID", "CLIENT_SECRET", "OPENAI_API_KEY", 
            "STATIC_PAGE_SUBDOMAIN", "MAXMIND_LICENSE_KEY"
        }
    }
    
    all_passed = True
    
    for filename, expected_vars in expected_env_vars.items():
        filepath = config_dir / filename
        package_name = filename.replace('.json', '').replace('+', '+')
        
        print(f"Testing {package_name} package...")
        
        if not filepath.exists():
            print(f"❌ Config file not found: {filename}")
            all_passed = False
            continue
        
        try:
            with open(filepath, 'r') as f:
                config = json.load(f)
            
            # Get all environment variables from the config
            actual_vars = set()
            for detail in config.get('details', []):
                for env_var in detail.get('required_env', []):
                    actual_vars.add(env_var)
            
            # Check if all expected variables are present
            missing_vars = expected_vars - actual_vars
            extra_vars = actual_vars - expected_vars
            
            if missing_vars:
                print(f"❌ Missing environment variables: {missing_vars}")
                all_passed = False
            
            if extra_vars:
                print(f"⚠️  Extra environment variables: {extra_vars}")
            
            if not missing_vars and not extra_vars:
                print(f"✅ {package_name} has correct environment variables")
            elif not missing_vars:
                print(f"✅ {package_name} has all required environment variables")
            
            print(f"   Required: {sorted(actual_vars)}")
            
        except json.JSONDecodeError as e:
            print(f"❌ Invalid JSON in {filename}: {e}")
            all_passed = False
        except Exception as e:
            print(f"❌ Error reading {filename}: {e}")
            all_passed = False
        
        print()
    
    return all_passed

def test_template_consistency():
    """Test that templates use the correct environment variable names"""
    print("🧪 Testing template consistency...\n")
    
    template_file = Path("backend/app/core/deployment/terraform_templates/config-template.toml.j2")
    
    if not template_file.exists():
        print("❌ Template file not found")
        return False
    
    with open(template_file, 'r') as f:
        content = f.read()
    
    # Check for correct environment variable usage
    expected_vars = [
        "STATIC_PAGE_SUBDOMAIN",
        "MAXMIND_LICENSE_KEY", 
        "KOMODO_PASSKEY"
    ]
    
    all_found = True
    for var in expected_vars:
        if var not in content:
            print(f"❌ Environment variable {var} not found in template")
            all_found = False
        else:
            print(f"✅ Found {var} in template")
    
    return all_found

def main():
    """Run all tests"""
    print("🧪 Testing 3-package implementation with correct environment variables...\n")
    
    config_test = test_package_configs()
    template_test = test_template_consistency()
    
    print("📊 Test Results:")
    print(f"Package configurations: {'✅ PASS' if config_test else '❌ FAIL'}")
    print(f"Template consistency: {'✅ PASS' if template_test else '❌ FAIL'}")
    
    if config_test and template_test:
        print("\n🎉 All tests passed! The package configurations are correct.")
        return True
    else:
        print("\n❌ Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    main()
