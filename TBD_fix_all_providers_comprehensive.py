#!/usr/bin/env python3
"""
Comprehensive fix for all providers and templates to ensure all environment variables are properly passed
"""
import os
import re
from pathlib import Path

def fix_provider_terraform_vars(filepath):
    """Fix _get_terraform_vars method in provider files"""
    print(f"Fixing terraform vars in {filepath}...")
    
    with open(filepath, 'r') as f:
        content = f.read()
    
    # Check if _get_terraform_vars method exists and needs fixing
    if '_get_terraform_vars' in content:
        # Add missing variables to the return dictionary
        missing_vars = [
            '"maxmind_license_key": deployment.maxmind_license_key,',
            '"komodo_passkey": deployment.komodo_passkey,'
        ]
        
        # Find the return dictionary and add missing variables
        pattern = r'("komodo_host_ip": deployment\.komodo_host_ip,)\s*\n\s*}'
        
        def add_missing_vars(match):
            existing_line = match.group(1)
            return f'{existing_line}\n            "maxmind_license_key": deployment.maxmind_license_key,\n            "komodo_passkey": deployment.komodo_passkey,\n        }}'
        
        content = re.sub(pattern, add_missing_vars, content)
    
    # Fix terraform.tfvars template rendering - add missing komodo_passkey
    if 'komodo_host_ip=deployment.komodo_host_ip' in content and 'komodo_passkey=deployment.komodo_passkey' not in content:
        content = re.sub(
            r'(komodo_host_ip=deployment\.komodo_host_ip)',
            r'\1,\n            komodo_passkey=deployment.komodo_passkey',
            content
        )
    
    with open(filepath, 'w') as f:
        f.write(content)
    
    print(f"✅ Fixed {filepath}")

def fix_terraform_tfvars_template(filepath):
    """Fix terraform.tfvars.j2 templates"""
    print(f"Fixing terraform.tfvars template {filepath}...")
    
    with open(filepath, 'r') as f:
        content = f.read()
    
    # Fix static_page_domain -> static_page_subdomain
    content = re.sub(r'static_page_domain', 'static_page_subdomain', content)
    
    # Replace the old conditional structure with the new one
    old_pattern = r'{% if package in \["Pangolin\+", "Pangolin\+AI"\] %}.*?{% endif %}'
    new_section = '''{% if package in ["Pangolin+", "Pangolin+AI"] %}
# Pangolin+ Package Configuration
crowdsec_enrollment_key = "{{ crowdsec_enrollment_key or '' }}"
static_page_subdomain   = "{{ static_page_subdomain or '' }}"
maxmind_license_key     = "{{ maxmind_license_key or '' }}"
{% endif %}

{% if package == "Pangolin+AI" %}
# Pangolin+AI Additional Configuration
oauth_client_id         = "{{ oauth_client_id or '' }}"
oauth_client_secret     = "{{ oauth_client_secret or '' }}"
komodo_host_ip          = "{{ komodo_host_ip or '' }}"
komodo_passkey          = "{{ komodo_passkey or '' }}"
openai_api_key          = "{{ openai_api_key or '' }}"
{% endif %}'''
    
    content = re.sub(old_pattern, new_section, content, flags=re.DOTALL)
    
    with open(filepath, 'w') as f:
        f.write(content)
    
    print(f"✅ Fixed {filepath}")

def fix_config_template(filepath):
    """Fix config-template.toml.j2 templates to include COMPONENTS"""
    print(f"Fixing config template {filepath}...")
    
    with open(filepath, 'r') as f:
        content = f.read()
    
    # Add COMPONENTS environment variable if not present
    if 'COMPONENTS=' not in content:
        # Find the line with POSTGRES_HOST and add COMPONENTS after it
        pattern = r'(POSTGRES_HOST=\{\{ postgres_host \}\})\n({% if package)'
        replacement = r'''\1
{% if package == "Pangolin" %}
COMPONENTS="pangolin"
{% elif package == "Pangolin+" %}
COMPONENTS="pangolin+,crowdsec,middleware-manager,static-page,traefik-log-dashboard"
{% elif package == "Pangolin+AI" %}
COMPONENTS="pangolin+,crowdsec,middleware-manager,komodo,mcpauth,nlweb,static-page,traefik-log-dashboard"
{% endif %}
\2'''
        content = re.sub(pattern, replacement, content)
    
    with open(filepath, 'w') as f:
        f.write(content)
    
    print(f"✅ Fixed {filepath}")

def main():
    """Apply comprehensive fixes to all providers and templates"""
    print("🔧 Applying comprehensive fixes to all providers and templates...\n")
    
    # Fix provider files
    providers_dir = Path("backend/app/core/deployment/providers")
    provider_files = [
        "awslightsail.py", "digitalocean.py", "gcp.py", 
        "hetzner.py", "linode.py", "byovps.py"
    ]
    
    for filename in provider_files:
        filepath = providers_dir / filename
        if filepath.exists():
            try:
                fix_provider_terraform_vars(filepath)
            except Exception as e:
                print(f"❌ Error fixing {filename}: {e}")
    
    # Fix terraform.tfvars templates
    templates_dir = Path("backend/app/core/deployment/terraform_templates")
    tfvars_templates = [
        "awslightsail_terraform.tfvars.j2", "digitalocean_terraform.tfvars.j2",
        "gcp_terraform.tfvars.j2", "hetzner_terraform.tfvars.j2", 
        "linode_terraform.tfvars.j2", "azure_terraform.tfvars.j2",
        "byovps_terraform.tfvars.j2"
    ]
    
    for filename in tfvars_templates:
        filepath = templates_dir / filename
        if filepath.exists():
            try:
                fix_terraform_tfvars_template(filepath)
            except Exception as e:
                print(f"❌ Error fixing {filename}: {e}")
    
    # Fix config templates
    config_templates = [
        "awslightsail_config-template.toml.j2", "digitalocean_config-template.toml.j2",
        "gcp_config-template.toml.j2", "hetzner_config-template.toml.j2",
        "linode_config-template.toml.j2", "azure_config-template.toml.j2"
    ]
    
    for filename in config_templates:
        filepath = templates_dir / filename
        if filepath.exists():
            try:
                fix_config_template(filepath)
            except Exception as e:
                print(f"❌ Error fixing {filename}: {e}")
    
    print("\n🎉 Comprehensive fixes applied to all providers and templates!")
    print("All providers should now correctly pass environment variables.")

if __name__ == "__main__":
    main()
