#!/usr/bin/env python3
"""
Fix all provider files to include the missing environment variables
"""
import os
import re
from pathlib import Path

def fix_provider_file(filepath):
    """Fix a single provider file"""
    print(f"Fixing {filepath}...")
    
    with open(filepath, 'r') as f:
        content = f.read()
    
    # Fix 1: Update package condition from "Premium" to new packages
    content = re.sub(
        r'deployment\.package == "Premium"',
        'deployment.package in ["Pangolin+", "Pangolin+AI"]',
        content
    )
    
    # Fix 2: Replace static_page_domain with static_page_subdomain
    content = re.sub(
        r'"static_page_domain":\s*deployment\.static_page_domain',
        '"static_page_subdomain": deployment.static_page_subdomain',
        content
    )
    content = re.sub(
        r'static_page_domain=deployment\.static_page_domain',
        'static_page_subdomain=deployment.static_page_subdomain',
        content
    )
    
    # Fix 3: Add missing fields to config template generation
    if '_generate_config_template' in content:
        # Add missing fields to template render call
        if 'maxmind_license_key=deployment.maxmind_license_key' not in content:
            content = re.sub(
                r'(static_page_subdomain=deployment\.static_page_subdomain,)',
                r'\1\n            maxmind_license_key=deployment.maxmind_license_key,',
                content
            )
        
        if 'komodo_passkey=deployment.komodo_passkey' not in content:
            content = re.sub(
                r'(komodo_host_ip=deployment\.komodo_host_ip,)',
                r'\1\n            komodo_passkey=deployment.komodo_passkey,',
                content
            )
    
    # Fix 4: Update terraform vars dictionary for new package structure
    if '_get_terraform_vars' in content:
        # Replace Premium package logic with new structure
        premium_pattern = r'if deployment\.package == "Premium":\s*\n\s*vars_dict\.update\(\{\s*\n(.*?)\s*\}\)'
        
        def replace_premium_logic(match):
            return '''if deployment.package in ["Pangolin+", "Pangolin+AI"]:
            vars_dict.update({
                "crowdsec_enrollment_key": deployment.crowdsec_enrollment_key,
                "static_page_subdomain": deployment.static_page_subdomain,
                "maxmind_license_key": deployment.maxmind_license_key,
            })
        if deployment.package == "Pangolin+AI":
            vars_dict.update({
                "oauth_client_id": deployment.oauth_client_id,
                "oauth_client_secret": deployment.oauth_client_secret,
                "openai_api_key": deployment.openai_api_key,
                "komodo_host_ip": deployment.komodo_host_ip,
                "komodo_passkey": deployment.komodo_passkey,
            })'''
        
        content = re.sub(premium_pattern, replace_premium_logic, content, flags=re.DOTALL)
    
    with open(filepath, 'w') as f:
        f.write(content)
    
    print(f"✅ Fixed {filepath}")

def main():
    """Fix all provider files"""
    print("🔧 Fixing all provider files...\n")
    
    providers_dir = Path("backend/app/core/deployment/providers")
    
    # List of provider files to fix
    provider_files = [
        "awslightsail.py",
        "digitalocean.py", 
        "gcp.py",
        "hetzner.py",
        "linode.py",
        "vultr.py",
        "byovps.py"
    ]
    
    for filename in provider_files:
        filepath = providers_dir / filename
        if filepath.exists():
            try:
                fix_provider_file(filepath)
            except Exception as e:
                print(f"❌ Error fixing {filename}: {e}")
        else:
            print(f"⚠️  File not found: {filename}")
    
    print("\n🎉 All provider files have been updated!")
    print("The missing environment variables should now be included in deployments.")

if __name__ == "__main__":
    main()
