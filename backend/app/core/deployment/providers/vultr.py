import os
from pathlib import Path
from jinja2 import Environment, FileSystemLoader
from app.core.deployment.providers.base import ProviderStrategy
from app.models.deployment import Deployment as DeploymentModel
from app.core.config import get_settings

class VultrStrategy(ProviderStrategy):
    """Concrete strategy for Vultr deployments."""

    def generate_terraform_files(self, deployment_dir: Path, deployment: DeploymentModel) -> None:
        """Generate all necessary Terraform files for a Vultr deployment."""
        template_dir = Path(__file__).parent.parent / "terraform_templates"
        env = Environment(loader=FileSystemLoader(template_dir))

        self._generate_vultr_main_files(deployment_dir, env, deployment)
        self._generate_tfvars(deployment_dir, env, deployment)
        self._generate_config_template(deployment_dir, env, deployment)

    def _generate_vultr_main_files(self, deployment_dir: Path, env: Environment, deployment: DeploymentModel) -> None:
        """Generate main Vultr Terraform files from templates."""
        main_template = env.get_template("vultr_main.tf.j2")
        variables_template = env.get_template("vultr_variables.tf.j2")
        outputs_template = env.get_template("vultr_outputs.tf.j2")
        startup_script_template = env.get_template("vultr_startup-script.sh")

        with open(deployment_dir / "main.tf", "w") as f:
            f.write(main_template.render(package=deployment.package))
        with open(deployment_dir / "variables.tf", "w") as f:
            f.write(variables_template.render(package=deployment.package))
        with open(deployment_dir / "outputs.tf", "w") as f:
            f.write(outputs_template.render(package=deployment.package))
        with open(deployment_dir / "startup-script.sh", "w") as f:
            f.write(startup_script_template.render())

    def _generate_tfvars(self, deployment_dir: Path, env: Environment, deployment: DeploymentModel) -> None:
        """Generate the terraform.tfvars file for Vultr."""
        ssh_public_key = ""

        # First priority: Use user's SSH key if provided
        if deployment.user_ssh_key:
            ssh_public_key = deployment.user_ssh_key.strip()
            print("Using user-provided SSH key")
        else:
            # Fallback to system SSH keys
            ssh_key_file_path = os.environ.get("VULTR_SSH_FILE")

            if ssh_key_file_path:
                try:
                    with open(ssh_key_file_path, 'r') as f:
                        ssh_public_key = f.read().strip()
                    print(f"Successfully read SSH key from {ssh_key_file_path}")
                except Exception as e:
                    print(f"Error reading SSH key file: {e}")

            if not ssh_public_key:
                ssh_public_key = get_settings().SSH_PUBLIC_KEY or ""

        client_name_lower = deployment.client_name.lower().replace(' ', '-')        

        template = env.get_template("vultr_terraform.tfvars.j2")
        tfvars_content = template.render(
            vultr_api_key=get_settings().VULTR_API_KEY,
            region=deployment.region,
            instance_type=deployment.instance_type,
            os_id="1743",  # Ubuntu 22.04
            instance_name=f"{client_name_lower}-instance",
            ssh_public_key=ssh_public_key,
            komodo_provider_endpoint=get_settings().KOMODO_PROVIDER_ENDPOINT,
            komodo_api_key=get_settings().KOMODO_API_KEY,
            komodo_api_secret=get_settings().KOMODO_API_SECRET,
            github_token=get_settings().GITHUB_TOKEN,
            domain=deployment.domain,
            admin_email=deployment.admin_email,
            admin_username=deployment.admin_username,
            admin_password=deployment.admin_password,
            admin_subdomain=deployment.admin_subdomain,
            postgres_user=deployment.postgres_user,
            postgres_password=deployment.postgres_password,
            postgres_host=deployment.postgres_host,
            github_repo=deployment.github_repo,
            package=deployment.package,
            crowdsec_enrollment_key=deployment.crowdsec_enrollment_key,
            static_page_subdomain=deployment.static_page_subdomain,
            maxmind_license_key=deployment.maxmind_license_key,
            oauth_client_id=deployment.oauth_client_id,
            oauth_client_secret=deployment.oauth_client_secret,
            client_id=deployment.client_id,
            client_name=deployment.client_name,
            client_name_lower=deployment.client_name.lower().replace(' ', '-'),
            create_firewall=True,
            openai_api_key=deployment.openai_api_key,
            komodo_host_ip=deployment.komodo_host_ip,
            komodo_passkey=deployment.komodo_passkey
        )
        with open(deployment_dir / "terraform.tfvars", "w") as f:
            f.write(tfvars_content)

    def _get_terraform_vars(self, deployment: DeploymentModel) -> dict:
        """Helper to construct a dictionary of Terraform variables."""
        ssh_public_key = ""

        # First priority: Use user's SSH key if provided
        if deployment.user_ssh_key:
            ssh_public_key = deployment.user_ssh_key.strip()
            print("Using user-provided SSH key")
        else:
            # Fallback to system SSH keys
            ssh_key_file_path = os.environ.get("VULTR_SSH_FILE")

            if ssh_key_file_path:
                try:
                    with open(ssh_key_file_path, 'r') as f:
                        ssh_public_key = f.read().strip()
                    print(f"Successfully read SSH key from {ssh_key_file_path}")
                except Exception as e:
                    print(f"Error reading SSH key file: {e}")

            if not ssh_public_key:
                ssh_public_key = get_settings().SSH_PUBLIC_KEY or ""
            
        client_name_lower = deployment.client_name.lower().replace(' ', '-')        
        return {
            "vultr_api_key": get_settings().VULTR_API_KEY,
            "region": deployment.region,
            "instance_type": deployment.instance_type,
            "os_id": "1743",  # Ubuntu 22.04
            "instance_name": f"{client_name_lower}-instance",
            "ssh_public_key": ssh_public_key,
            "komodo_provider_endpoint": get_settings().KOMODO_PROVIDER_ENDPOINT,
            "komodo_api_key": get_settings().KOMODO_API_KEY,
            "komodo_api_secret": get_settings().KOMODO_API_SECRET,
            "github_token": get_settings().GITHUB_TOKEN,
            "domain": deployment.domain,
            "admin_email": deployment.admin_email,
            "admin_username": deployment.admin_username,
            "admin_password": deployment.admin_password,
            "admin_subdomain": deployment.admin_subdomain,
            "postgres_user": deployment.postgres_user,
            "postgres_password": deployment.postgres_password,
            "postgres_host": deployment.postgres_host,
            "github_repo": deployment.github_repo,
            "crowdsec_enrollment_key": deployment.crowdsec_enrollment_key,
            "static_page_subdomain": deployment.static_page_subdomain,
            "maxmind_license_key": deployment.maxmind_license_key,
            "oauth_client_id": deployment.oauth_client_id,
            "oauth_client_secret": deployment.oauth_client_secret,
            "openai_api_key": deployment.openai_api_key,
            "komodo_host_ip": deployment.komodo_host_ip,
            "komodo_passkey": deployment.komodo_passkey,
        }

    def _generate_config_template(self, deployment_dir: Path, env: Environment, deployment: DeploymentModel) -> None:
        """Generate the config-template.toml file."""
        template = env.get_template("vultr_config-template.toml.j2")
        content = template.render(
            client_name=deployment.client_name,
            client_name_lower=deployment.client_name.lower().replace(' ', '-'),
            domain=deployment.domain,
            admin_email=deployment.admin_email,
            admin_username=deployment.admin_username,
            admin_password=deployment.admin_password,
            admin_subdomain=deployment.admin_subdomain,
            postgres_user=deployment.postgres_user,
            postgres_password=deployment.postgres_password,
            postgres_host=deployment.postgres_host,
            github_repo=deployment.github_repo,
            package=deployment.package,
            crowdsec_enrollment_key=deployment.crowdsec_enrollment_key,
            static_page_subdomain=deployment.static_page_subdomain,
            maxmind_license_key=deployment.maxmind_license_key,
            oauth_client_id=deployment.oauth_client_id,
            oauth_client_secret=deployment.oauth_client_secret,
            komodo_host_ip=deployment.komodo_host_ip,
            komodo_passkey=deployment.komodo_passkey,
            openai_api_key=deployment.openai_api_key,
        )
        with open(deployment_dir / "config-template.toml", "w") as f:
            f.write(content)
