from pathlib import Path
from jinja2 import Environment, FileSystemLoader
from app.core.deployment.providers.base import ProviderStrategy
from app.models.deployment import Deployment as DeploymentModel
from app.core.config import get_settings
from app.core.deployment.package_utils import normalize_package_name, is_premium_package

class BYOVPSStrategy(ProviderStrategy):
    """Strategy to deploy only Komodo resources to an existing server IP (BYOVPS/existing)."""

    def generate_terraform_files(self, deployment_dir: Path, deployment: DeploymentModel) -> None:
        template_dir = Path(__file__).parent.parent / "terraform_templates"
        env = Environment(loader=FileSystemLoader(template_dir))

        self._generate_main_files(deployment_dir, env, deployment)
        self._generate_tfvars(deployment_dir, env, deployment)
        self._generate_config_template(deployment_dir, env, deployment)

    def _generate_main_files(self, deployment_dir: Path, env: Environment, deployment: DeploymentModel) -> None:
        main_template = env.get_template("byovps_main.tf.j2")
        variables_template = env.get_template("byovps_variables.tf.j2")

        with open(deployment_dir / "main.tf", "w") as f:
            f.write(main_template.render())
        with open(deployment_dir / "variables.tf", "w") as f:
            f.write(variables_template.render(package=normalize_package_name(deployment.package)))

    def _generate_tfvars(self, deployment_dir: Path, env: Environment, deployment: DeploymentModel) -> None:
        template = env.get_template("byovps_terraform.tfvars.j2")
        tfvars_content = template.render(**self._get_terraform_vars(deployment))
        with open(deployment_dir / "terraform.tfvars", "w") as f:
            f.write(tfvars_content)

    def _get_terraform_vars(self, deployment: DeploymentModel) -> dict:
        client_name_lower = deployment.client_name.lower().replace(' ', '-') if deployment.client_name else "client"
        vars_dict = {
            "client_name": deployment.client_name,
            "client_name_lower": client_name_lower,
            "client_id": deployment.client_id,
            "package": normalize_package_name(deployment.package),
            "komodo_provider_endpoint": get_settings().KOMODO_PROVIDER_ENDPOINT,
            "komodo_api_key": get_settings().KOMODO_API_KEY,
            "komodo_api_secret": get_settings().KOMODO_API_SECRET,
            "github_token": get_settings().GITHUB_TOKEN,
            "domain": deployment.domain,
            "admin_email": deployment.admin_email,
            "admin_username": deployment.admin_username,
            "admin_password": deployment.admin_password,
            "admin_subdomain": deployment.admin_subdomain,
            "postgres_user": deployment.postgres_user,
            "postgres_password": deployment.postgres_password,
            "postgres_host": deployment.postgres_host,
            "github_repo": deployment.github_repo,
            "server_ip": deployment.vps_ip_address or deployment.komodo_host_ip or "",
        }
        if is_premium_package(deployment.package):
            vars_dict.update({
                "crowdsec_enrollment_key": deployment.crowdsec_enrollment_key,
                "static_page_subdomain": deployment.static_page_subdomain,
                "oauth_client_id": deployment.oauth_client_id,
                "oauth_client_secret": deployment.oauth_client_secret,
                "openai_api_key": deployment.openai_api_key,
                "komodo_host_ip": deployment.komodo_host_ip or "",
            })
        return vars_dict

    def _generate_config_template(self, deployment_dir: Path, env: Environment, deployment: DeploymentModel) -> None:
        template = env.get_template("config-template.toml.j2")
        content = template.render(
            client_name=deployment.client_name,
            client_name_lower=(deployment.client_name or "client").lower().replace(' ', '-'),
            domain=deployment.domain,
            admin_email=deployment.admin_email,
            admin_username=deployment.admin_username,
            admin_password=deployment.admin_password,
            admin_subdomain=deployment.admin_subdomain,
            postgres_user=deployment.postgres_user,
            postgres_password=deployment.postgres_password,
            postgres_host=deployment.postgres_host,
            github_repo=deployment.github_repo,
            package=normalize_package_name(deployment.package),
            crowdsec_enrollment_key=deployment.crowdsec_enrollment_key,
            static_page_subdomain=deployment.static_page_subdomain,
            maxmind_license_key=deployment.maxmind_license_key,
            oauth_client_id=deployment.oauth_client_id,
            oauth_client_secret=deployment.oauth_client_secret,
            komodo_host_ip=deployment.komodo_host_ip,
            komodo_passkey=deployment.komodo_passkey,
            openai_api_key=deployment.openai_api_key,
        )
        with open(deployment_dir / "config-template.toml", "w") as f:
            f.write(content)

