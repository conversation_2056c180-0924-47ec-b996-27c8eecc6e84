variable "linode_token" {
  description = "Linode API token"
  type        = string
  sensitive   = true
}

variable "region" {
  description = "Linode region for the instance"
  type        = string
  default     = "us-east"
}

variable "type" {
  description = "Linode instance type"
  type        = string
  default     = "g6-standard-1"
}

variable "image" {
  description = "Operating system image"
  type        = string
  default     = "linode/ubuntu22.04"
}

variable "root_password" {
  description = "Root password for the instance"
  type        = string
  sensitive   = true
}

variable "ssh_public_key" {
  description = "SSH public key for server access"
  type        = string
}

variable "allowed_source_ips" {
  description = "List of allowed source IPs for firewall rules"
  type        = list(string)
  default     = ["0.0.0.0/0"]
}

variable "create_firewall" {
  description = "Whether to create a firewall for the instance"
  type        = bool
  default     = true
}

variable "instance_name" {
  description = "Name of the Linode instance"
  type        = string
}

variable "client_name" {
  description = "Client's name"
  type        = string
}

variable "client_name_lower" {
  description = "Lowercase version of the client's name"
  type        = string
}

variable "client_id" {
  description = "Unique identifier for the client"
  type        = string
}

variable "domain" {
  description = "Domain name for the application"
  type        = string
}

variable "admin_email" {
  description = "Admin email address"
  type        = string
}

variable "admin_username" {
  description = "Admin username"
  type        = string
}

variable "admin_password" {
  description = "Admin password"
  type        = string
  sensitive   = true
}

variable "admin_subdomain" {
  description = "Admin panel subdomain"
  type        = string
  default     = "admin"
}

variable "postgres_user" {
  description = "PostgreSQL username"
  type        = string
}

variable "postgres_password" {
  description = "PostgreSQL password"
  type        = string
  sensitive   = true
}

variable "postgres_host" {
  description = "PostgreSQL host address"
  type        = string
}

variable "github_repo" {
  description = "GitHub repository for client resources"
  type        = string
}

variable "komodo_provider_endpoint" {
  description = "Komodo provider API endpoint"
  type        = string
}

variable "komodo_api_key" {
  description = "Komodo API key"
  type        = string
  sensitive   = true
}

variable "komodo_api_secret" {
  description = "Komodo API secret"
  type        = string
  sensitive   = true
}

variable "github_token" {
  description = "GitHub token for authentication"
  type        = string
  sensitive   = true
}

{% if package in ["Pangolin+", "Pangolin+AI"] %}
variable "crowdsec_enrollment_key" {
  description = "CrowdSec Enrollment key"
  type        = string
  sensitive   = true
}

variable "static_page_subdomain" {
  description = "Static page subdomain"
  type        = string
}

variable "maxmind_license_key" {
  description = "MaxMind license key for GeoIP"
  type        = string
  sensitive   = true
}
{% endif %}

{% if package == "Pangolin+AI" %}
variable "oauth_client_id" {
  description = "OAuth client ID"
  type        = string
  sensitive   = true
}

variable "oauth_client_secret" {
  description = "OAuth client secret"
  type        = string
  sensitive   = true
}

variable "komodo_host_ip" {
  description = "Komodo host IP for AI features"
  type        = string
}

variable "komodo_passkey" {
  description = "Komodo passkey for authentication"
  type        = string
  sensitive   = true
}

variable "openai_api_key" {
  description = "OpenAI API key"
  type        = string
  sensitive   = true
}
{% endif %}
