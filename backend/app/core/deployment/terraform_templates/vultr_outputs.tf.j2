output "instance_ip" {
  description = "Public IP address of the Vultr instance"
  value       = vultr_instance.pangolin.main_ip
}

output "instance_name" {
  description = "Name of the Vultr instance"
  value       = vultr_instance.pangolin.label
}

output "instance_id" {
  description = "ID of the Vultr instance"
  value       = vultr_instance.pangolin.id
}

output "instance_status" {
  description = "Status of the Vultr instance"
  value       = vultr_instance.pangolin.status
}

output "instance_region" {
  description = "Region of the Vultr instance"
  value       = vultr_instance.pangolin.region
}

output "instance_plan" {
  description = "Plan of the Vultr instance"
  value       = vultr_instance.pangolin.plan
}

output "client_id" {
  description = "Client ID"
  value       = var.client_id
}

output "client_name" {
  description = "Client name"
  value       = var.client_name
}

output "domain" {
  description = "Application domain"
  value       = var.domain
}

output "firewall_group_id" {
  description = "ID of the Vultr firewall group"
  value       = var.create_firewall ? vultr_firewall_group.web[0].id : null
}

output "ssh_key_id" {
  description = "ID of the SSH key"
  value       = vultr_ssh_key.default.id
}

output "server_ip" {
  value = vultr_instance.pangolin.main_ip
}
