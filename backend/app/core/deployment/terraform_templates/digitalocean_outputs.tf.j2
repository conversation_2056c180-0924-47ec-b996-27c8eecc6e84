output "instance_ip" {
  description = "Public IPv4 address of the DigitalOcean Droplet"
  value       = digitalocean_droplet.pangolin.ipv4_address
}

output "instance_name" {
  description = "Name of the DigitalOcean Droplet"
  value       = digitalocean_droplet.pangolin.name
}

output "instance_id" {
  description = "ID of the DigitalOcean Droplet"
  value       = digitalocean_droplet.pangolin.id
}

output "instance_status" {
  description = "Status of the DigitalOcean Droplet"
  value       = digitalocean_droplet.pangolin.status
}

output "instance_region" {
  description = "Region of the DigitalOcean Droplet"
  value       = digitalocean_droplet.pangolin.region
}

output "instance_plan" {
  description = "Size (plan) of the DigitalOcean Droplet"
  value       = digitalocean_droplet.pangolin.size
}

output "client_id" {
  description = "Client ID"
  value       = var.client_id
}

output "client_name" {
  description = "Client name"
  value       = var.client_name
}

output "domain" {
  description = "Application domain"
  value       = var.domain
}

output "firewall_id" {
  description = "ID of the DigitalOcean Firewall"
  value       = var.create_firewall ? digitalocean_firewall.web[0].id : null
}

output "ssh_key_id" {
  description = "ID of the SSH key"
  value       = digitalocean_ssh_key.default.id
}