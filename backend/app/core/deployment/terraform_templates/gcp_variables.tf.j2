# GCP Provider Configuration
variable "gcp_project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "gcp_region" {
  description = "The GCP region"
  type        = string
  default     = "us-central1"
}

variable "gcp_zone" {
  description = "The GCP zone"
  type        = string
  default     = "us-central1-a"
}

variable "gcp_credentials_file" {
  description = "Path to the GCP service account key file"
  type        = string
  default     = ""
  sensitive   = true
}

# Instance Configuration
variable "instance_name" {
  description = "Name of the GCP compute instance"
  type        = string
}

variable "machine_type" {
  description = "Machine type for the GCP instance"
  type        = string
  default     = "e2-medium"
}

variable "instance_tags" {
  description = "Network tags for the instance"
  type        = list(string)
  default     = ["gcp-client"]
}

# SSH Configuration
variable "ssh_public_key" {
  description = "SSH public key for instance access"
  type        = string
}

variable "ssh_username" {
  description = "Username for SSH access"
  type        = string
  default     = "ubuntu"
}

# Firewall Configuration
variable "firewall_name" {
  description = "Name of the firewall rule"
  type        = string
  default     = "allow-ssh-and-8120"
}

variable "allowed_ports" {
  description = "List of ports to allow through firewall"
  type        = list(string)
  default     = ["22", "443", "8120", "9120"]
}

variable "allowed_udp_ports" {
  description = "List of UDP ports to allow through firewall"
  type        = list(string)
  default     = ["51820"]
}

variable "firewall_source_ranges" {
  description = "Source IP ranges for firewall rule"
  type        = list(string)
  default     = ["0.0.0.0/0"]
}

# Client Information
variable "client_id" {
  description = "ID of the client"
  type        = string
}

variable "client_name" {
  description = "Name of the client"
  type        = string
}

variable "client_name_lower" {
  description = "Lowercase name of the client"
  type        = string
  default     = ""
}

variable "package" {
  description = "Package type (Pangolin, Pangolin+, Pangolin+AI)"
  type        = string
}

# Komodo provider variables
variable "komodo_provider_endpoint" {
  description = "Endpoint for the Komodo provider"
  type        = string
}

variable "komodo_api_key" {
  description = "API key for the Komodo provider"
  type        = string
  sensitive   = true
}

variable "komodo_api_secret" {
  description = "API secret for the Komodo provider"
  type        = string
  sensitive   = true
}

variable "github_token" {
  description = "GitHub token"
  type        = string
  sensitive   = true
}

# Application configuration variables
variable "domain" {
  description = "Domain for the application"
  type        = string
}

variable "admin_email" {
  description = "Email for the admin user"
  type        = string
}

variable "admin_username" {
  description = "Username for the admin user"
  type        = string
}

variable "admin_password" {
  description = "Password for the admin user"
  type        = string
  sensitive   = true
}

variable "admin_subdomain" {
  description = "Subdomain for the admin interface"
  type        = string
  default     = "admin"
}

variable "postgres_user" {
  description = "PostgreSQL username"
  type        = string
}

variable "postgres_password" {
  description = "PostgreSQL password"
  type        = string
  sensitive   = true
}

variable "postgres_host" {
  description = "PostgreSQL host"
  type        = string
}

variable "github_repo" {
  description = "GitHub repository for the application"
  type        = string
}

{% if package in ["Pangolin+", "Pangolin+AI"] %}
variable "crowdsec_enrollment_key" {
  description = "CrowdSec Enrollment key"
  type        = string
  sensitive   = true
}

variable "static_page_subdomain" {
  description = "Static page subdomain"
  type        = string
}

variable "maxmind_license_key" {
  description = "MaxMind license key for GeoIP"
  type        = string
  sensitive   = true
}
{% endif %}

{% if package == "Pangolin+AI" %}
variable "oauth_client_id" {
  description = "OAuth client ID"
  type        = string
  sensitive   = true
}

variable "oauth_client_secret" {
  description = "OAuth client secret"
  type        = string
  sensitive   = true
}

variable "komodo_host_ip" {
  description = "Komodo host IP for AI features"
  type        = string
}

variable "komodo_passkey" {
  description = "Komodo passkey for authentication"
  type        = string
  sensitive   = true
}

variable "openai_api_key" {
  description = "OpenAI API key"
  type        = string
  sensitive   = true
}
{% endif %}

