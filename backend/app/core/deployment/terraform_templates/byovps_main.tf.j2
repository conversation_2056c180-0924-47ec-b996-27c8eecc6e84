# Komodo provider only - no cloud resources

terraform {
  required_providers {
    komodo-provider = {
      source  = "registry.example.com/mattercoder/komodo-provider"
      version = ">= 1.0.0"
    }
  }
}

provider "komodo-provider" {
  endpoint     = var.komodo_provider_endpoint
  api_key      = var.komodo_api_key
  api_secret   = var.komodo_api_secret
  github_token = var.github_token
}

# Register the user's resources and point to existing server IP
resource "komodo-provider_user" "client_syncresources" {
  id   = var.client_id
  name = var.client_name

  file_contents = templatefile("${path.module}/config-template.toml", {
    client_name_lower        = lower(var.client_name)
    client_name              = var.client_name
    domain                   = var.domain
    admin_email              = var.admin_email
    admin_username           = var.admin_username
    admin_password           = var.admin_password
    admin_subdomain          = var.admin_subdomain
    crowdsec_enrollment_key  = var.crowdsec_enrollment_key
    postgres_user            = var.postgres_user
    postgres_password        = var.postgres_password
    postgres_host            = var.postgres_host
    github_repo              = var.github_repo
    static_page_subdomain = var.static_page_domain
    oauth_client_id          = var.oauth_client_id
    oauth_client_secret      = var.oauth_client_secret
    komodo_host_ip           = var.komodo_host_ip
    openai_api_key           = var.openai_api_key
  })

  server_ip = var.server_ip
}

