
terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "=4.37.0"
    }
    komodo-provider = {
      source = "registry.example.com/mattercoder/komodo-provider"
      version = ">= 1.0.0"
    }
  }
}

# Azure Provider
provider "azurerm" {
  features {
    resource_group {
      prevent_deletion_if_contains_resources = false
    }
  }
  subscription_id = var.azure_subscription_id
  client_id       = var.azure_client_id
  client_secret   = var.azure_client_secret
  tenant_id       = var.azure_tenant_id
}


# Resource Group
resource "azurerm_resource_group" "client_rg" {
  name     = "${var.client_name}-resources"
  location = var.azure_location
}

# Virtual Network
resource "azurerm_virtual_network" "client_vnet" {
  name                = "${var.client_name}-network"
  address_space       = ["10.0.0.0/16"]
  location            = azurerm_resource_group.client_rg.location
  resource_group_name = azurerm_resource_group.client_rg.name
  
  timeouts {
    create = "30m"
    delete = "30m"
  }
}

# Subnet
resource "azurerm_subnet" "client_subnet" {
  name                 = "internal"
  resource_group_name  = azurerm_resource_group.client_rg.name
  virtual_network_name = azurerm_virtual_network.client_vnet.name
  address_prefixes     = ["********/24"]
  
  depends_on = [
    azurerm_virtual_network.client_vnet
  ]
  
  timeouts {
    create = "30m"
    delete = "30m"
  }
}

# Network Security Group
resource "azurerm_network_security_group" "client_nsg" {
  name                = "${var.client_name}-nsg"
  location            = azurerm_resource_group.client_rg.location
  resource_group_name = azurerm_resource_group.client_rg.name

  security_rule {
    name                       = "SSH"
    priority                   = 1001
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "22"
    source_address_prefix      = "*"
    destination_address_prefix = "*"
  }

  security_rule {
    name                       = "HTTP"
    priority                   = 1002
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "80"
    source_address_prefix      = "*"
    destination_address_prefix = "*"
  }

  security_rule {
    name                       = "HTTPS"
    priority                   = 1003
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "443"
    source_address_prefix      = "*"
    destination_address_prefix = "*"
  }

  security_rule {
    name                       = "CustomHTTP"
    priority                   = 1004
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "8120"
    source_address_prefix      = "*"
    destination_address_prefix = "*"
  }

  security_rule {
    name                       = "CustomAPI"
    priority                   = 1005
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "9120"
    source_address_prefix      = "*"
    destination_address_prefix = "*"
  }

  security_rule {
    name                       = "CustomTunnel"
    priority                   = 1006
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Udp"
    source_port_range          = "*"
    destination_port_range     = "51820"
    source_address_prefix      = "*"
    destination_address_prefix = "*"
  }

}

# Associate NSG with Subnet
resource "azurerm_subnet_network_security_group_association" "client_subnet_nsg_association" {
  subnet_id                 = azurerm_subnet.client_subnet.id
  network_security_group_id = azurerm_network_security_group.client_nsg.id
}

# Network Interface
resource "azurerm_network_interface" "client_nic" {
  name                = "${var.client_name}-nic"
  location            = azurerm_resource_group.client_rg.location
  resource_group_name = azurerm_resource_group.client_rg.name

  ip_configuration {
    name                          = "internal"
    subnet_id                     = azurerm_subnet.client_subnet.id
    private_ip_address_allocation = "Dynamic"
    public_ip_address_id          = azurerm_public_ip.client_public_ip.id
  }
  
  # Add explicit dependency
  depends_on = [
    azurerm_subnet.client_subnet,
    azurerm_public_ip.client_public_ip
  ]
}

# Associate NSG with Network Interface
resource "azurerm_network_interface_security_group_association" "client_nic_nsg_association" {
  network_interface_id      = azurerm_network_interface.client_nic.id
  network_security_group_id = azurerm_network_security_group.client_nsg.id
}

# Public IP
resource "azurerm_public_ip" "client_public_ip" {
  name                = "${var.client_name}-public-ip"
  location            = azurerm_resource_group.client_rg.location
  resource_group_name = azurerm_resource_group.client_rg.name
  allocation_method   = "Static"
  sku                 = "Standard"  # Change from Basic to Standard
}

# Linux Virtual Machine
resource "azurerm_linux_virtual_machine" "client_vm" {
  name                = var.instance_name
  resource_group_name = azurerm_resource_group.client_rg.name
  location            = azurerm_resource_group.client_rg.location
  size                = var.vm_size
  admin_username      = var.ssh_username
  network_interface_ids = [
    azurerm_network_interface.client_nic.id,
  ]

  admin_ssh_key {
    username   = var.ssh_username
    public_key = var.ssh_public_key
  }

  os_disk {
    caching              = "ReadWrite"
    storage_account_type = "Standard_LRS"
  }

  source_image_reference {
    publisher = "Canonical"
    offer     = "ubuntu-24_04-lts"
    sku       = "server"
    version   = "latest"
  }

  custom_data = base64encode(templatefile("${path.module}/startup-script.sh", {}))

  tags = {
    environment = "production"
    client      = var.client_name
  }
}

# Custom User Provider
provider "komodo-provider" {
  endpoint     = var.komodo_provider_endpoint
  api_key      = var.komodo_api_key
  api_secret   = var.komodo_api_secret
  github_token = var.github_token
}

# Custom provider resource with templated configuration
resource "komodo-provider_user" "client_syncresources" {
  depends_on = [azurerm_linux_virtual_machine.client_vm]

  id                = var.client_id
  name              = var.client_name
  file_contents = templatefile("${path.module}/config-template.toml", {
    client_name_lower        = lower(var.client_name)
    client_name             = var.client_name
    domain                  = var.domain
    admin_email             = var.admin_email
    admin_username          = var.admin_username
    admin_password          = var.admin_password
    admin_subdomain         = var.admin_subdomain
    postgres_user           = var.postgres_user
    postgres_password       = var.postgres_password
    postgres_host           = var.postgres_host
    github_repo             = var.github_repo
    {% if package in ["Pangolin+", "Pangolin+AI"] %}
    crowdsec_enrollment_key = var.crowdsec_enrollment_key
    static_page_subdomain   = var.static_page_subdomain
    maxmind_license_key     = var.maxmind_license_key
    {% endif %}
    {% if package == "Pangolin+AI" %}
    oauth_client_id         = var.oauth_client_id
    oauth_client_secret     = var.oauth_client_secret
    komodo_host_ip          = azurerm_public_ip.client_public_ip.ip_address
    komodo_passkey          = var.komodo_passkey
    openai_api_key          = var.openai_api_key
    {% endif %}
  })
  server_ip = azurerm_public_ip.client_public_ip.ip_address
}
