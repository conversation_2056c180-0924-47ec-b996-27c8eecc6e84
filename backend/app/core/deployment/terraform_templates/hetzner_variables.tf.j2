# Hetzner Cloud Provider Variables
variable "hcloud_token" {
  description = "Hetzner Cloud API Token"
  type        = string
  sensitive   = true
}

variable "location" {
  description = "Hetzner Cloud location (e.g., nbg1, fsn1, hel1)"
  type        = string
  default     = "nbg1"
}

variable "server_type" {
  description = "Hetzner Cloud server type (e.g., cx11, cx21)"
  type        = string
  default     = "cx11"
}

variable "os_type" {
  description = "Operating system image"
  type        = string
  default     = "ubuntu-22.04"
}

variable "instance_name" {
  description = "Name of the server instance"
  type        = string
}

variable "client_name" {
  description = "Name of the client"
  type        = string
}

variable "client_name_lower" {
  description = "Lowercase name of the client"
  type        = string
  default     = ""
}

variable "client_id" {
  description = "ID of the client"
  type        = string
}

variable "create_firewall" {
  description = "Whether to create a firewall"
  type        = bool
  default     = true
}

variable "allowed_source_ips" {
  description = "List of allowed source IPs for the firewall"
  type        = list(string)
  default     = ["0.0.0.0/0"]
}

variable "ssh_public_key" {
  description = "SSH public key for server access"
  type        = string
}

variable "package" {
  description = "Package type (Pangolin, Pangolin+, Pangolin+AI)"
  type        = string
}

# Komodo provider variables
variable "komodo_provider_endpoint" {
  description = "Endpoint for the Komodo provider"
  type        = string
}

variable "komodo_api_key" {
  description = "API key for the Komodo provider"
  type        = string
  sensitive   = true
}

variable "komodo_api_secret" {
  description = "API secret for the Komodo provider"
  type        = string
  sensitive   = true
}

variable "github_token" {
  description = "GitHub token"
  type        = string
  sensitive   = true
}

# Application configuration variables
variable "domain" {
  description = "Domain for the application"
  type        = string
}

variable "admin_email" {
  description = "Email for the admin user"
  type        = string
}

variable "admin_username" {
  description = "Username for the admin user"
  type        = string
}

variable "admin_password" {
  description = "Password for the admin user"
  type        = string
  sensitive   = true
}

variable "admin_subdomain" {
  description = "Subdomain for the admin interface"
  type        = string
  default     = "admin"
}

variable "postgres_user" {
  description = "PostgreSQL username"
  type        = string
}

variable "postgres_password" {
  description = "PostgreSQL password"
  type        = string
  sensitive   = true
}

variable "postgres_host" {
  description = "PostgreSQL host"
  type        = string
}

variable "github_repo" {
  description = "GitHub repository for the application"
  type        = string
}

{% if package in ["Pangolin+", "Pangolin+AI"] %}
variable "crowdsec_enrollment_key" {
  description = "CrowdSec Enrollment key"
  type        = string
  sensitive   = true
}

variable "static_page_subdomain" {
  description = "Static page subdomain"
  type        = string
}

variable "maxmind_license_key" {
  description = "MaxMind license key for GeoIP"
  type        = string
  sensitive   = true
}
{% endif %}

{% if package == "Pangolin+AI" %}
variable "oauth_client_id" {
  description = "OAuth client ID"
  type        = string
  sensitive   = true
}

variable "oauth_client_secret" {
  description = "OAuth client secret"
  type        = string
  sensitive   = true
}

variable "komodo_host_ip" {
  description = "Komodo host IP for AI features"
  type        = string
}

variable "komodo_passkey" {
  description = "Komodo passkey for authentication"
  type        = string
  sensitive   = true
}

variable "openai_api_key" {
  description = "OpenAI API key"
  type        = string
  sensitive   = true
}
{% endif %}
