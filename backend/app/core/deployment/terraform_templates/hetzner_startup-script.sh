#!/bin/bash
set -e

# Log all output to a file for debugging
exec > >(tee /var/log/user-data.log) 2>&1

echo "Starting user data script execution at $(date)"

# Create a user for easier SSH access (optional, since root access via SSH key should work)
# Set a random root password for emergency access
ROOT_PASSWORD=$(openssl rand -base64 32)
echo "root:$ROOT_PASSWORD" | chpasswd
echo "Root password set to: $ROOT_PASSWORD" >> /root/credentials.txt

# Create SSH access instructions
echo "=== SSH ACCESS INSTRUCTIONS ===" >> /root/ssh_instructions.txt
echo "To connect to this server, use:" >> /root/ssh_instructions.txt
echo "ssh -i /path/to/your/private/key root@$(curl -s http://***************/metadata/v1/interfaces/public/0/ipv4/address)" >> /root/ssh_instructions.txt
echo "Root password (for emergency console access): $ROOT_PASSWORD" >> /root/ssh_instructions.txt
echo "===============================" >> /root/ssh_instructions.txt

# Enable password authentication for SSH (optional, for emergency access)
sed -i 's/#PasswordAuthentication no/PasswordAuthentication yes/' /etc/ssh/sshd_config
sed -i 's/PasswordAuthentication no/PasswordAuthentication yes/' /etc/ssh/sshd_config
systemctl restart sshd

# Update system packages
apt-get update

# Install required packages
apt-get install -y \
  ca-certificates \
  curl \
  gnupg \
  lsb-release \
  git \
  postgresql-client

# Add Docker's official GPG key
mkdir -p /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg

# Set up the Docker repository
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \
  $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null

# Install Docker Engine
apt-get update
apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# Create docker group and add default user
usermod -aG docker root

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/download/v2.20.3/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Create docker network for pangolin
docker network create pangolin

# Create komodo directory structure
mkdir -p /etc/komodo/stacks

# Install Python and required packages
apt-get install -y python3 python3-pip
pip3 install requests toml pyyaml

echo "Starting komodo periphery at $(date)"
# Run setup script for komodo periphery
curl -sSL https://raw.githubusercontent.com/moghtech/komodo/main/scripts/setup-periphery.py | sudo python3

# Enable periphery service
systemctl enable periphery

echo "User data script finished at $(date)"