output "instance_ip" {
  description = "Public IPv4 address of the AWS Lightsail Instance"
  value       = aws_lightsail_instance.pangolin.public_ip_address
}

output "instance_name" {
  description = "Name of the AWS Lightsail Instance"
  value       = aws_lightsail_instance.pangolin.name
}

output "instance_id" {
  description = "ID of the AWS Lightsail Instance"
  value       = aws_lightsail_instance.pangolin.id
}

output "instance_region_and_az" {
  description = "Availability Zone of the AWS Lightsail Instance (includes region)"
  value       = aws_lightsail_instance.pangolin.availability_zone
}

output "instance_plan" {
  description = "Bundle (plan/size) ID of the AWS Lightsail Instance"
  value       = aws_lightsail_instance.pangolin.bundle_id
}

output "client_id" {
  description = "Client ID"
  value       = var.client_id
}

output "client_name" {
  description = "Client name"
  value       = var.client_name
}

output "domain" {
  description = "Application domain"
  value       = var.domain
}

output "firewall_info" {
  description = "Information about the Lightsail firewall rules (managed on the instance directly)"
  value = var.create_firewall ? (
    "Firewall rules applied to instance: ${aws_lightsail_instance.pangolin.name}"
  ) : null
}

output "ssh_key_id" {
  description = "ID of the AWS Lightsail Key Pair"
  value       = aws_lightsail_key_pair.default.id
}