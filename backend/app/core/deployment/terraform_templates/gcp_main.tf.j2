
terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 4.0"
    }
    komodo-provider = {
      source = "registry.example.com/mattercoder/komodo-provider"
      version = ">= 1.0.0"
    }
  }
}

provider "google" {
  project = var.gcp_project_id
  region  = var.gcp_region
  credentials = file(var.gcp_credentials_file)
}

resource "google_compute_instance" "default" {
  name         = var.instance_name
  machine_type = var.machine_type
  zone         = var.gcp_zone
  allow_stopping_for_update = true

  boot_disk {
    initialize_params {
      image = "ubuntu-os-cloud/ubuntu-2204-lts"
    }
  }

  network_interface {
    network = "default"
    access_config {
      // Ephemeral IP
    }
  }

  metadata = {
    ssh-keys = "${var.ssh_username}:${var.ssh_public_key}"
    startup-script = templatefile("${path.module}/startup-script.sh", {})
  }
  tags = var.instance_tags
}

# Firewall rule to allow SSH and application port
resource "google_compute_firewall" "allow_ports" {
  name    = var.firewall_name
  network = "default"

  allow {
    protocol = "tcp"
    ports    = var.allowed_ports
  }

  allow {
    protocol = "udp"
    ports    = var.allowed_udp_ports
  }

  source_ranges = var.firewall_source_ranges
  target_tags   = var.instance_tags
}

# Custom User Provider
provider "komodo-provider" {
  endpoint     = var.komodo_provider_endpoint
  api_key      = var.komodo_api_key
  api_secret   = var.komodo_api_secret
  github_token = var.github_token
}

# Custom provider resource with templated configuration
resource "komodo-provider_user" "client_syncresources" {
  depends_on = [google_compute_instance.default]

  id                = var.client_id
  name              = var.client_name
  file_contents = templatefile("${path.module}/config-template.toml", {
    client_name_lower        = lower(var.client_name)
    client_name             = var.client_name
    domain                  = var.domain
    admin_email             = var.admin_email
    admin_username          = var.admin_username
    admin_password          = var.admin_password
    admin_subdomain         = var.admin_subdomain
    postgres_user           = var.postgres_user
    postgres_password       = var.postgres_password
    postgres_host           = var.postgres_host
    github_repo             = var.github_repo
    {% if package in ["Pangolin+", "Pangolin+AI"] %}
    crowdsec_enrollment_key = var.crowdsec_enrollment_key
    static_page_subdomain   = var.static_page_subdomain
    maxmind_license_key     = var.maxmind_license_key
    {% endif %}
    {% if package == "Pangolin+AI" %}
    oauth_client_id         = var.oauth_client_id
    oauth_client_secret     = var.oauth_client_secret
    komodo_passkey          = var.komodo_passkey
    openai_api_key          = var.openai_api_key
    {% endif %}
  })
  server_ip = google_compute_instance.default.network_interface[0].access_config[0].nat_ip
}
