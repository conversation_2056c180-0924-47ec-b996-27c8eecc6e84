def normalize_package_name(package: str) -> str:
    """
    Normalize package name for template compatibility.
    Keep the new package names as-is since templates have been updated.
    """
    return package

def is_premium_package(package: str) -> bool:
    """
    Check if a package is an advanced package (Pangolin+ or Pangolin+AI).
    """
    return package in ["Pangolin+", "Pangolin+AI"]

def is_ai_package(package: str) -> bool:
    """
    Check if a package includes AI features.
    """
    return package == "Pangolin+AI"