import os
import logging
import shutil
from pathlib import Path
from typing import cast
from app.models.deployment import Deployment as DeploymentModel
from app.db.session import SessionLocal
from app.core.deployment.providers.base import ProviderStrategy
from app.core.deployment.providers.gcp import GoogleCloudStrategy
from app.core.deployment.providers.hetzner import HetznerStrategy
from app.core.deployment.providers.linode import LinodeStrategy
from app.core.deployment.providers.vultr import VultrStrategy
from app.core.deployment.providers.digitalocean import DigitalOceanStrategy
from app.core.deployment.providers.awslightsail import AwsLightsailStrategy
from app.core.deployment.providers.azure import AzureStrategy
from app.core.deployment.providers.byovps import BYOVPSStrategy
from app.core.deployment.terraform_runner import TerraformRunner
from app.services.billing_job import run_billing_for_user_deployment

class DeploymentManager:
    def __init__(self):
        self.provider_strategies = {
            "Google Cloud": GoogleCloudStrategy(),
            "Hetzner": HetznerStrategy(),
            "Linode": LinodeStrategy(),
            "Vultr": VultrStrategy(),
            "DigitalOcean": DigitalOceanStrategy(),
            "AWS Lightsail": AwsLightsailStrategy(),
            "Azure": AzureStrategy(),
            "BYOVPS": BYOVPSStrategy(),
        }

    def _get_provider_strategy(self, provider_name: str) -> ProviderStrategy:
        strategy = self.provider_strategies.get(provider_name)
        if not strategy:
            raise ValueError(f"Unsupported cloud provider: {provider_name}")
        return strategy

    def deploy(self, deployment_id: int, dry_run: bool = False) -> str:
        """Deploy the infrastructure using Terraform. On any failure, attempt to destroy created resources."""
        db = SessionLocal()
        deployment = None
        deployment_dir: Path | None = None
        try:
            deployment = db.query(DeploymentModel).filter(DeploymentModel.id == deployment_id).first()
            if not deployment:
                raise ValueError(f"Deployment with ID {deployment_id} not found.")

            dry_run = dry_run or os.getenv('TERRAFORM_DRY_RUN', '').lower() in ('true', '1', 'yes')
            deployment_dir = self._create_deployment_directory(deployment)

            provider_key = cast(str, deployment.cloud_provider)
            if getattr(deployment, 'server_type', 'new') in ('vps', 'existing'):
                provider_key = 'BYOVPS'
            strategy = self._get_provider_strategy(provider_key)
            strategy.generate_terraform_files(deployment_dir, deployment)

            terraform_runner = TerraformRunner(deployment_dir)
            terraform_output = terraform_runner.init_and_apply(dry_run)

            assert deployment is not None
            if terraform_output and 'komodo_host_ip' in terraform_output:
                deployment.komodo_host_ip = terraform_output['komodo_host_ip']['value'] # type: ignore

            # Capture instance IP from terraform outputs
            if terraform_output:
                # Try different output names used by different providers
                instance_ip = None
                for ip_key in ['instance_ip', 'server_ip', 'instance_public_ip', 'server_ipv4']:
                    if ip_key in terraform_output:
                        instance_ip = terraform_output[ip_key]['value']
                        break

                if instance_ip:
                    deployment.instance_ip = instance_ip # type: ignore

            deployment.status = "ACTIVE" # type: ignore
            db.add(deployment)
            db.commit()
            
            # Trigger billing refresh for this deployment to ensure up-to-date calculations
            try:
                run_billing_for_user_deployment(deployment.user_id, deployment.id)
                logging.info(f"Billing refresh completed for deployment {deployment.id}")
            except Exception as billing_error:
                # Don't fail the deployment if billing refresh fails, just log it
                logging.warning(f"Billing refresh failed for deployment {deployment.id}: {billing_error}")
            
            return "Deployment successful"
        except Exception as e:
            logging.error(f"Deployment failed: {e}")
            # Best-effort cleanup: attempt destroy to avoid dangling resources
            try:
                if deployment_dir is not None:
                    TerraformRunner(deployment_dir).destroy()
            except Exception as destroy_err:
                logging.error(f"Automatic destroy after failure also failed: {destroy_err}")
            finally:
                if deployment is not None:
                    deployment.status = "FAILED" # type: ignore
                    db.add(deployment)
                    db.commit()
            raise
        finally:
            db.close()

    def destroy(self, deployment_id: int):
        """Destroy the Terraform-managed infrastructure."""
        db = SessionLocal()
        try:
            deployment = db.query(DeploymentModel).filter(DeploymentModel.id == deployment_id).first()
            if not deployment:
                raise ValueError(f"Deployment with ID {deployment_id} not found.")

            # --- Final Billing Logic ---
            # Perform a final billing run to ensure the last hour of usage is charged.
            from datetime import datetime, timedelta, timezone
            from decimal import Decimal
            from app.models.transaction import Transaction
            import logging

            print(f"Performing final billing for deployment {deployment.id}...")
            try:
                # This logic is adapted from billing_job.py to be synchronous
                now_utc = datetime.now(timezone.utc)

                created_at_utc = deployment.created_at
                if created_at_utc.tzinfo is None:
                    created_at_utc = created_at_utc.replace(tzinfo=timezone.utc)

                if deployment.last_billed_at:
                    start_to_check_from = deployment.last_billed_at.replace(tzinfo=timezone.utc) if deployment.last_billed_at.tzinfo is None else deployment.last_billed_at
                    current_hour_to_check = start_to_check_from + timedelta(hours=1)
                else:
                    current_hour_to_check = created_at_utc.replace(minute=0, second=0, microsecond=0)

                last_successfully_billed_hour = None

                while current_hour_to_check < now_utc:
                    if created_at_utc > current_hour_to_check + timedelta(hours=1):
                        current_hour_to_check += timedelta(hours=1)
                        continue

                    hourly_cost_cents = deployment.cost or 0
                    charge_eur = Decimal(hourly_cost_cents) / Decimal(100)

                    if charge_eur > Decimal('0.00'):
                        user = deployment.user
                        if not user:
                            break

                        user.balance = (user.balance or Decimal("0")) - charge_eur
                        tx_description = f"Final usage charge for deployment #{deployment.id} (Hour: {current_hour_to_check.strftime('%Y-%m-%d %H:00')})"
                        tx = Transaction(user_id=user.id, amount=-charge_eur, type="billing", description=tx_description)
                        
                        db.add(user)
                        db.add(tx)
                        
                        last_successfully_billed_hour = current_hour_to_check
                    
                    current_hour_to_check += timedelta(hours=1)

                if last_successfully_billed_hour:
                    deployment.last_billed_at = last_successfully_billed_hour
                    db.add(deployment)
                
                db.commit()
                print(f"Final billing for deployment {deployment.id} complete.")

            except Exception as billing_err:
                db.rollback()
                logging.error(f"Could not perform final billing for deployment {deployment.id}: {billing_err}")
            # --- End of Final Billing ---

            deployment_dir = Path(f"backend/deployments/{deployment.id}")
            if not deployment_dir.exists():
                raise FileNotFoundError(f"Deployment directory not found: {deployment_dir}")

            terraform_runner = TerraformRunner(deployment_dir)
            terraform_runner.destroy()

            shutil.rmtree(deployment_dir)
            print(f"Deployment directory {deployment_dir} removed.")

            assert deployment is not None
            deployment.status = "DESTROYED" # type: ignore
            db.add(deployment)
            db.commit()
            
            # Trigger billing refresh for final charge calculation when deployment is destroyed
            try:
                run_billing_for_user_deployment(deployment.user_id, deployment.id)
                logging.info(f"Final billing refresh completed for destroyed deployment {deployment.id}")
            except Exception as billing_error:
                # Don't fail the destroy process if billing refresh fails, just log it
                logging.warning(f"Final billing refresh failed for destroyed deployment {deployment.id}: {billing_error}")
        finally:
            db.close()

    def _create_deployment_directory(self, deployment: DeploymentModel) -> Path:
        """Create a directory for the deployment files."""
        deployment_dir = Path(f"backend/deployments/{deployment.id}")
        deployment_dir.mkdir(parents=True, exist_ok=True)
        return deployment_dir