import csv
import sys
import os
from pathlib import Path

# Add the parent directory to the path so we can import app modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.db.session import SessionLocal
from app.models.instance_pricing import InstancePricing

def load_pricing_data():
    """Load pricing data from CSV into the database"""
    # Fix the path to look for the CSV file in the root config folder
    csv_path = Path(__file__).parent.parent.parent / "config" / "pricing.csv"
    if not os.path.exists(csv_path):
        print(f"Error: Pricing CSV file not found at {csv_path}")
        return
    
    db = SessionLocal()
    try:
        # Clear existing pricing data
        db.query(InstancePricing).delete()
        
        # Load new data from CSV
        with open(csv_path, 'r') as file:
            reader = csv.DictReader(file)
            for row in reader:
                pricing = InstancePricing(
                    cloud_provider=row['cloud_provider'],
                    instance_type=row['instance_type'],
                    region=row['region'],
                    cost_per_hour=float(row['hourly_cost']),
                    package_multiplier_pangolin=float(row['package_multiplier_pangolin']),
                    package_multiplier_pangolin_plus=float(row['package_multiplier_pangolin+']),
                    package_multiplier_pangolin_plus_ai=float(row['package_multiplier_pangolin+AI']),
                    support_cost_level1=float(row['support_cost_level1']),
                    support_cost_level2=float(row['support_cost_level2']),
                    support_cost_level3=float(row['support_cost_level3'])
                )
                db.add(pricing)
        
        db.commit()
        print(f"Successfully loaded pricing data from {csv_path}")
    except Exception as e:
        db.rollback()
        print(f"Error loading pricing data: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    load_pricing_data()
