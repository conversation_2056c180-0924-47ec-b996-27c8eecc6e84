
from app.db.base import Base
from app.db.session import engine
# Import all models here so that Base has them registered
from app.models.user import User
from app.models.deployment import Deployment
from app.models.transaction import Transaction
from app.models.instance_pricing import InstancePricing

def create_tables():
    print("Creating tables...")
    Base.metadata.create_all(bind=engine)
    print("Tables created.")

if __name__ == "__main__":
    create_tables()
