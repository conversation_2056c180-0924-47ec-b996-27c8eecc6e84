"""
Migration script to add instance_ip and user_ssh_key columns to deployments table.
This script can be run to add the new columns to existing databases.
"""

from sqlalchemy import create_engine, text
from app.core.config import get_settings
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_add_instance_ip():
    """Add instance_ip and user_ssh_key columns to deployments table if they don't exist."""
    settings = get_settings()
    engine = create_engine(settings.DATABASE_URL)
    
    try:
        with engine.connect() as conn:
            # Check if instance_ip column already exists
            result = conn.execute(text("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'deployments'
                AND column_name = 'instance_ip'
            """))

            if result.fetchone() is None:
                # Column doesn't exist, add it
                logger.info("Adding instance_ip column to deployments table...")
                conn.execute(text("""
                    ALTER TABLE deployments
                    ADD COLUMN instance_ip VARCHAR NULL
                """))
                conn.commit()
                logger.info("Successfully added instance_ip column to deployments table")
            else:
                logger.info("instance_ip column already exists in deployments table")

            # Check if user_ssh_key column already exists
            result = conn.execute(text("""
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'deployments'
                AND column_name = 'user_ssh_key'
            """))

            if result.fetchone() is None:
                # Column doesn't exist, add it
                logger.info("Adding user_ssh_key column to deployments table...")
                conn.execute(text("""
                    ALTER TABLE deployments
                    ADD COLUMN user_ssh_key VARCHAR NULL
                """))
                conn.commit()
                logger.info("Successfully added user_ssh_key column to deployments table")
            else:
                logger.info("user_ssh_key column already exists in deployments table")
                
    except Exception as e:
        logger.error(f"Error during migration: {e}")
        raise

if __name__ == "__main__":
    migrate_add_instance_ip()
