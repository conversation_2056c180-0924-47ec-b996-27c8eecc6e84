import asyncio
import socket
from typing import Tuple

PERIPHERY_PORT = 8120  # periphery agent port
PERIPHERY_HEALTH_PATH = "/health"  # if available in periphery
PERIPHERY_MIN_VERSION = "0.1.0"


class VPSValidationService:
    async def validate_periphery_client(self, ip_address: str) -> Tuple[bool, str]:
        """
        Attempt to connect to the periphery client on the VPS to validate connectivity.
        Strategy:
        - Try TCP connect to PERIPHERY_PORT with short timeout
        - Optionally try HTTP GET to /health to check version
        """
        # First: TCP connectivity check
        tcp_ok, tcp_msg = await self._tcp_connect(ip_address, PERIPHERY_PORT, timeout=5)
        if not tcp_ok:
            return False, f"Cannot connect to periphery on {ip_address}:{PERIPHERY_PORT} - {tcp_msg}"

        # If we want a stronger check, try a simple HTTP GET to health endpoint
        try:
            import http.client
            conn = http.client.HTTPConnection(ip_address, PERIPHERY_PORT, timeout=5)
            conn.request("GET", PERIPHERY_HEALTH_PATH)
            resp = conn.getresponse()
            if resp.status == 200:
                return True, "Periphery client reachable and healthy"
            else:
                return True, f"Periphery TCP reachable; health endpoint returned {resp.status}"
        except Exception:
            # Health endpoint may not exist; TCP reachability is still OK
            return True, "Periphery client reachable"

    async def _tcp_connect(self, host: str, port: int, timeout: int = 5) -> Tuple[bool, str]:
        loop = asyncio.get_running_loop()
        try:
            fut = loop.getaddrinfo(host, port, type=socket.SOCK_STREAM)
            infos = await asyncio.wait_for(fut, timeout=timeout)
            for family, socktype, proto, canonname, sockaddr in infos:
                try:
                    reader, writer = await asyncio.wait_for(asyncio.open_connection(host=sockaddr[0], port=sockaddr[1]), timeout=timeout)
                    writer.close()
                    await writer.wait_closed()
                    return True, "connected"
                except Exception as e:
                    last_err = str(e)
                    continue
            return False, last_err if 'last_err' in locals() else 'unknown error'
        except Exception as e:
            return False, str(e)

    def generate_periphery_script(self) -> str:
        """Return a Docker Compose configuration for users to run on their VPS to install and start periphery."""
        import os

        # Try to read KOMODO_PASSKEY and KOMODO_PROVIDER_IP from frontend .env file first, then backend
        komodo_passkey = 'your_komodo_passkey'
        komodo_provider_ip = 'your_komodo_provider_ip'

        # Try to read from frontend .env file
        frontend_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'frontend', '.env')
        if os.path.exists(frontend_env_path):
            try:
                with open(frontend_env_path, 'r') as f:
                    for line in f:
                        if line.strip().startswith('KOMODO_PASSKEY='):
                            komodo_passkey = line.strip().split('=', 1)[1]
                        elif line.strip().startswith('KOMODO_PROVIDER_IP='):
                            komodo_provider_ip = line.strip().split('=', 1)[1]
            except Exception:
                pass

        # Fallback to backend settings
        if komodo_passkey == 'your_komodo_passkey':
            from app.core.config import get_settings
            settings = get_settings()
            komodo_passkey = getattr(settings, 'KOMODO_PASSKEY', 'your_komodo_passkey')

        return f"""####################################
# 🦎 KOMODO COMPOSE - PERIPHERY 🦎 #
####################################

## This compose file will deploy theKomodo Periphery
##
## We rely on Komodo for managing your VPS's Docker containers.
## Learn more about Komodo at https://komo.do/docs/connect-servers#configuration
## You need to make folder for komodo using mkdir -p /etc/komodo or set with PERIPHERY_ROOT_DIRECTORY

services:
  periphery:
    image: ghcr.io/moghtech/komodo-periphery:${{COMPOSE_KOMODO_IMAGE_TAG:-latest}}
    labels:
      komodo.skip: # Prevent Komodo from stopping with StopAllContainers
    restart: unless-stopped
    environment:
      PERIPHERY_ROOT_DIRECTORY: ${{PERIPHERY_ROOT_DIRECTORY:-/etc/komodo}}
      ## Pass the same passkey as used by the Komodo Core connecting to this Periphery agent.
      PERIPHERY_PASSKEYS: {komodo_passkey}
      ## Restrict access to specific IP addresses (Komodo Core server)
      PERIPHERY_ALLOWED_IPS: "{komodo_provider_ip}"
      ## Make server run over https
      PERIPHERY_SSL_ENABLED: true
      ## Specify whether to disable the terminals feature
      ## and disallow remote shell access (inside the Periphery container).
      PERIPHERY_DISABLE_TERMINALS: false
      PERIPHERY_INCLUDE_DISK_MOUNTS: /etc/hostname
    volumes:
      ## Mount external docker socket
      - /var/run/docker.sock:/var/run/docker.sock
      ## Allow Periphery to see processes outside of container
      - /proc:/proc
      - ${{PERIPHERY_ROOT_DIRECTORY:-/etc/komodo}}:${{PERIPHERY_ROOT_DIRECTORY:-/etc/komodo}}
    ## If periphery is being run remote from the core server, ports need to be exposed
    ports:
      - 8120:8120"""

