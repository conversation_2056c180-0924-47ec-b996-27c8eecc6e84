from datetime import datetime, timedelta, timezone
from decimal import Decimal
from math import ceil
from sqlalchemy.orm import Session

from app.db.session import SessionLocal
from app.core.config import get_settings
from app.models.user import User
from app.models.deployment import Deployment
from app.models.transaction import Transaction


def _process_deployments_billing(deployments_to_process, now, db, settings):
    """
    Shared billing logic for processing a list of deployments.
    
    Args:
        deployments_to_process: List of deployment objects to process
        now: Current datetime for billing calculations
        db: Database session
        settings: Application settings
        
    Returns:
        Dictionary with billing summary information
    """
    summary = {"charged_users": 0, "terminated_deployments": 0, "errors": []}
    users_to_check = set()

    # Get billing interval from settings
    billing_interval_minutes = getattr(settings, 'BILLING_INTERVAL_MINUTES', 60)
    billing_interval = timedelta(minutes=billing_interval_minutes)

    for d in deployments_to_process:
        try:
            # This logic implements a configurable billing interval model,
            # billing for discrete time blocks without double-charging.

            # Ensure the deployment's creation time is timezone-aware to prevent comparison errors.
            created_at_utc = d.created_at
            if created_at_utc.tzinfo is None:
                created_at_utc = created_at_utc.replace(tzinfo=timezone.utc)

            # Determine the starting interval for the check.
            if d.last_billed_at:
                # If already billed, start checking from the *next* billing interval.
                start_to_check_from = d.last_billed_at.replace(tzinfo=timezone.utc) if d.last_billed_at.tzinfo is None else d.last_billed_at
                current_interval_to_check = start_to_check_from + billing_interval
            else:
                # If never billed, start with the interval block containing the creation time.
                # For intervals >= 1 hour, align to hour boundaries; for smaller intervals, align to creation time
                if billing_interval_minutes >= 60:
                    current_interval_to_check = created_at_utc.replace(minute=0, second=0, microsecond=0)
                else:
                    current_interval_to_check = created_at_utc

            now_utc = now or datetime.now(timezone.utc)
            last_successfully_billed_interval = None

            # Loop through each billing interval from our starting point up to the current time.
            while current_interval_to_check < now_utc:
                # Skip if the deployment was created *after* this entire interval block ended.
                if created_at_utc > current_interval_to_check + billing_interval:
                    current_interval_to_check += billing_interval
                    continue

                # This is a billable interval.
                hourly_cost_cents = d.cost or 0
                
                # Calculate the charge based on the billing interval
                # cost is stored as hourly cost in cents, so we need to adjust for the billing interval
                interval_multiplier = Decimal(billing_interval_minutes) / Decimal(60)  # Convert to hour fraction
                charge_eur = (Decimal(hourly_cost_cents) / Decimal(100)) * interval_multiplier

                if charge_eur <= Decimal('0.00'):
                    current_interval_to_check += billing_interval
                    continue
                
                user = d.user
                if not user:
                    break  # Exit while loop for this deployment

                user.balance = (user.balance or Decimal("0")) - charge_eur
                
                # Format the interval description based on the billing period
                if billing_interval_minutes >= 60:
                    if billing_interval_minutes == 60:
                        interval_desc = f"Hour: {current_interval_to_check.strftime('%Y-%m-%d %H:00')}"
                    elif billing_interval_minutes == 1440:  # 24 hours
                        interval_desc = f"Day: {current_interval_to_check.strftime('%Y-%m-%d')}"
                    else:
                        interval_desc = f"Period: {current_interval_to_check.strftime('%Y-%m-%d %H:%M')} ({billing_interval_minutes}min)"
                else:
                    interval_desc = f"Period: {current_interval_to_check.strftime('%Y-%m-%d %H:%M')} ({billing_interval_minutes}min)"
                
                tx_description = f"Usage charge for deployment #{d.id} ({interval_desc})"
                tx = Transaction(user_id=user.id, amount=-charge_eur, type="billing", description=tx_description)
                
                db.add(user)
                db.add(tx)
                users_to_check.add(user.id)
                
                last_successfully_billed_interval = current_interval_to_check

                current_interval_to_check += billing_interval

            if last_successfully_billed_interval:
                d.last_billed_at = last_successfully_billed_interval
                db.add(d)
            
            db.commit()

        except Exception as e:
            db.rollback()
            error_msg = f"Error billing deployment {d.id}: {e}"
            print(error_msg)
            summary["errors"].append(error_msg)
    
    summary["charged_users"] = len(users_to_check)

    # --- Termination Check ---
    # Check balances for all users who were charged
    for user_id in users_to_check:
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            continue

        # Check for low balance
        balance_float = float(user.balance or 0)
        if 0 < balance_float < settings.MIN_BALANCE_TO_DEPLOY:
            print(f"[LOW BALANCE] User {user.id} balance is low: {balance_float:.2f} EUR")

        # Terminate if balance is zero or negative
        if balance_float <= 0:
            print(f"[TERMINATION] User {user.id} balance is {balance_float:.2f} EUR. Initiating termination.")
            # Get all active deployments for this user, most expensive first
            deployments_to_terminate = db.query(Deployment).filter(
                Deployment.user_id == user.id,
                Deployment.status == 'ACTIVE',
                Deployment.deleted_at.is_(None)
            ).order_by(Deployment.cost.desc()).all()

            from app.core.deployment.deployment_manager import DeploymentManager
            manager = DeploymentManager()
            for dep in deployments_to_terminate:
                try:
                    print(f"Terminating deployment {dep.id} for user {user.id}...")
                    manager.destroy(int(dep.id))
                    
                    dep.deleted_at = now
                    dep.status = 'DELETED'
                    
                    term_tx = Transaction(
                        user_id=user.id,
                        amount=Decimal('0'),
                        type="termination",
                        description=f"Deployment #{dep.id} terminated due to insufficient balance."
                    )
                    db.add(dep)
                    db.add(term_tx)
                    db.commit()
                    
                    summary["terminated_deployments"] += 1
                    print(f"Termination of deployment {dep.id} successful.")
                    
                    # Re-fetch user and check if balance is now positive
                    db.refresh(user)
                    if (user.balance or 0) > 0:
                        print(f"User {user.id} balance is now positive. Stopping termination cycle for this user.")
                        break
                
                except Exception as e:
                    db.rollback()
                    error_msg = f"[AUTO-TERMINATE] Failed to destroy deployment {dep.id}: {e}"
                    print(error_msg)
                    summary["errors"].append(error_msg)
    
    return summary


def run_billing_for_user_deployment(user_id: int, deployment_id: int | None = None, now: datetime | None = None) -> dict:
    """
    Runs billing for a specific user's deployments or a specific deployment.
    
    This function can be called immediately when a deployment is created to ensure
    users see up-to-date billing calculations.
    
    Args:
        user_id: The user ID to run billing for
        deployment_id: Optional specific deployment ID to bill (if None, bills all user's deployments)
        now: An optional datetime to override the current time, used for testing
        
    Returns:
        A summary dictionary logging the results of the billing operation.
    """
    settings = get_settings()
    now = now or datetime.now(timezone.utc)
    db: Session = SessionLocal()
    summary = {"charged_users": 0, "terminated_deployments": 0, "errors": []}
    
    try:
        # Get deployments to bill
        query = db.query(Deployment).filter(
            Deployment.user_id == user_id,
            Deployment.status == 'ACTIVE',
            Deployment.deleted_at.is_(None)
        )
        
        if deployment_id:
            query = query.filter(Deployment.id == deployment_id)
            
        deployments_to_bill = query.all()
        
        if not deployments_to_bill:
            return summary
            
        # Use the same billing logic as the main billing cycle
        result = _process_deployments_billing(deployments_to_bill, now, db, settings)
        summary.update(result)
        
        return summary
    finally:
        db.close()


def run_billing_cycle(now: datetime | None = None) -> dict:
    """
    Runs the billing cycle for all active deployments.

    This function is designed to be run periodically via a cron job or scheduler.
    It is idempotent and robust, ensuring that users are billed accurately even if runs are missed or repeated.

    - Calculates charges for 'ACTIVE' deployments based on configurable billing intervals since the last billing timestamp.
    - Deducts charges from user balances.
    - Automatically terminates deployments if a user's balance falls to or below zero.
    - Creates detailed transaction records for all billing and termination events.

    Args:
        now: An optional datetime to override the current time, used for testing.

    Returns:
        A summary dictionary logging the results of the cycle.
    """
    settings = get_settings()
    now = now or datetime.now(timezone.utc)
    db: Session = SessionLocal()
    
    try:
        # Get all deployments that are active and not marked as deleted
        active_deployments = db.query(Deployment).filter(
            Deployment.status == 'ACTIVE',
            Deployment.deleted_at.is_(None)
        ).all()

        # Use the shared billing logic
        summary = _process_deployments_billing(active_deployments, now, db, settings)
        
        return summary
    finally:
        db.close()


if __name__ == "__main__":
    print("Starting billing cycle...")
    result = run_billing_cycle()
    print(f"Billing cycle complete: {result}")

