from sqlalchemy import Column, String, Boolean, Numeric
from sqlalchemy.orm import relationship
from app.db.base import BaseModel

class User(BaseModel):
    __tablename__ = "users"

    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    is_admin = Column(Boolean, default=False, nullable=False)
    # Account balance in EUR with 2 decimal places
    balance = Column(Numeric(12, 2), nullable=False, default=0)

    deployments = relationship("Deployment", back_populates="user", uselist=True)
