from fastapi import APIRouter, Depends, BackgroundTasks, HTTPException
from datetime import datetime
from sqlalchemy.orm import Session
import json
import os
from pathlib import Path
from app.schemas.deployment import DeploymentCreate, Deployment, DeploymentBase, DeploymentUpdate, ServerType
from app.db.session import <PERSON>Local
from app.models.deployment import Deployment as DeploymentModel
from app.models.user import User as UserModel
from app.core.deployment.deployment_manager import DeploymentManager
from app.api.auth import get_current_user, get_admin_user
from app.schemas.user import User
from app.core.security import verify_api_key
from app.core.pricing.pricing_service import PricingService
from app.core.config import get_settings
from app.services.billing_job import run_billing_for_user_deployment

# Add the dependency to the router itself to apply to all endpoints
router = APIRouter(dependencies=[Depends(verify_api_key)])

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@router.get("/packages/")
def get_packages():
    return ["Pangolin", "Pangolin+", "Pangolin+AI"]

@router.get("/packages/{package_name}/config")
def get_package_config(package_name: str):
    """Get the configuration for a specific package"""
    # Map package names to file names
    package_file_mapping = {
        "Pangolin": "pangolin.json",
        "Pangolin+": "pangolin+.json",
        "Pangolin+AI": "pangolin+AI.json"
    }

    if package_name not in package_file_mapping:
        raise HTTPException(status_code=404, detail="Package not found")

    # Try multiple possible locations for the package config files
    possible_paths = [
        Path(__file__).parent.parent.parent / "config" / "packages" / package_file_mapping[package_name],
        Path(__file__).parent.parent / "config" / "packages" / package_file_mapping[package_name],
    ]

    config_path = None
    for path in possible_paths:
        if os.path.exists(path):
            config_path = path
            break

    if not config_path:
        raise HTTPException(status_code=404, detail=f"Package configuration file not found for {package_name}")

    try:
        with open(config_path, 'r') as file:
            config = json.load(file)
        return config
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error reading package configuration: {str(e)}")

@router.get("/features", response_model=dict)
def get_features():
    settings = get_settings()
    return {"allow_vps_reuse": bool(getattr(settings, "ALLOW_VPS_REUSE", False))}

pricing_service = PricingService()

@router.get("/cloud-providers/")
def get_cloud_providers():
    # Extract unique providers from pricing data
    providers = set(item['cloud_provider'] for item in pricing_service.pricing_data)
    return [{"name": provider, "display_name": provider} for provider in providers]

@router.get("/regions/{provider}")
def get_regions(provider: str):
    # Extract unique regions for the given provider
    regions = set(
        item['region'] for item in pricing_service.pricing_data
        if item['cloud_provider'] == provider
    )
    return [{"name": region, "display_name": region} for region in regions]

@router.get("/instance-types/{provider}/{region}")
def get_instance_types(provider: str, region: str):
    # Extract instance types for the given provider and region
    instance_types = []
    for item in pricing_service.pricing_data:
        if item['cloud_provider'] == provider and item['region'] == region:
            instance_types.append({
                "name": item['instance_type'],
                "display_name": item['instance_type'],
                "cpu": 2,  # Default values - could be added to CSV
                "memory": 4,  # Default values - could be added to CSV
                "hourly_cost": float(item['hourly_cost'])
            })
    return instance_types

@router.post("/pricing/calculate", response_model=dict)
def calculate_price(deployment: DeploymentBase):
    """Calculate pricing based on deployment configuration using the pricing service"""
    pricing_result = pricing_service.calculate_pricing(
        cloud_provider=deployment.cloud_provider,
        instance_type=deployment.instance_type,
        region=deployment.region,
        package=deployment.package,
        support_level=deployment.support_level,
        server_type=str(deployment.server_type)
    )

    return pricing_result

@router.post("/", response_model=Deployment)
async def create_deployment(deployment: DeploymentCreate, background_tasks: BackgroundTasks, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    settings = get_settings()
    # Enforce feature flag: disallow VPS reuse and existing server reuse when disabled
    if not settings.ALLOW_VPS_REUSE and deployment.server_type in (ServerType.existing):
        raise HTTPException(status_code=400, detail="Reusing an existing server is disabled by configuration")

    # Validate based on server_type
    if deployment.server_type == ServerType.new:
        if not deployment.cloud_provider or not deployment.region or not deployment.instance_type:
            raise HTTPException(status_code=400, detail="cloud_provider, region and instance_type are required for new server deployments")
        if deployment.cloud_provider == "Hetzner":
            if not settings.HCLOUD_TOKEN:
                raise HTTPException(status_code=400, detail="Hetzner Cloud API token is missing in environment variables")
    elif deployment.server_type == ServerType.vps:
        if not deployment.vps_ip_address:
            raise HTTPException(status_code=400, detail="vps_ip_address is required for BYOVPS deployments")
        # Optional: perform a quick validation now or require prior validation endpoint
    elif deployment.server_type == ServerType.existing:
        if not deployment.existing_server_id:
            raise HTTPException(status_code=400, detail="existing_server_id is required for reusing an existing server")

    cost_data = calculate_price(deployment)

    # Store hourly cost in cents for billing
    hourly_cost_cents = int(cost_data['hourly_cost'] * 100)

    # Enforce minimum balance requirement to create deployment
    min_balance = get_settings().MIN_BALANCE_TO_DEPLOY
    # refetch User model to check numeric balance
    db_user = db.query(UserModel).filter(UserModel.id == current_user.id).first()
    if db_user is not None:
        user_balance = float(db_user.balance or 0)
        if user_balance < min_balance:
            raise HTTPException(status_code=402, detail=f"Insufficient balance. Minimum {min_balance} EUR required to create a deployment.")

    db_deployment = DeploymentModel(**deployment.dict(), user_id=current_user.id, cost=hourly_cost_cents, status='CREATING')
    db.add(db_deployment)
    db.commit()
    db.refresh(db_deployment)

    deployment_manager = DeploymentManager()
    background_tasks.add_task(deployment_manager.deploy, int(db_deployment.id))

    return db_deployment


@router.delete("/{deployment_id}", response_model=Deployment)
async def delete_deployment(
    deployment_id: int, 
    background_tasks: BackgroundTasks, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    db_deployment = db.query(DeploymentModel).filter(DeploymentModel.id == deployment_id).first()
    if not db_deployment:
        raise HTTPException(status_code=404, detail="Deployment not found")
    
    # Check ownership (admin can delete any, user can only delete their own)
    if not current_user.is_admin and db_deployment.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to delete this deployment")
    
    if db_deployment.status != 'ACTIVE':
        raise HTTPException(status_code=400, detail=f"Cannot delete deployment with status: {db_deployment.status}")

    deployment_manager = DeploymentManager()
    background_tasks.add_task(deployment_manager.destroy, int(db_deployment.id))

    db_deployment.deleted_at = datetime.now() # type: ignore
    db.add(db_deployment)
    db.commit()
    db.refresh(db_deployment)
    
    # Trigger billing refresh to calculate any final charges for this deployment
    background_tasks.add_task(run_billing_for_user_deployment, current_user.id, db_deployment.id)
    
    return db_deployment


@router.get("/{deployment_id}/status", response_model=str)
async def get_deployment_status(
    deployment_id: int, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    db_deployment = db.query(DeploymentModel).filter(DeploymentModel.id == deployment_id).first()
    if not db_deployment:
        raise HTTPException(status_code=404, detail="Deployment not found")
    
    # Check ownership (admin can view any, user can only view their own)
    if not current_user.is_admin and db_deployment.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to view this deployment")
    
    return db_deployment.status



@router.patch("/{deployment_id}", response_model=Deployment)
async def update_deployment(deployment_id: int, deployment_update: DeploymentUpdate, background_tasks: BackgroundTasks, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    db_deployment = db.query(DeploymentModel).filter(DeploymentModel.id == deployment_id).first()
    if not db_deployment:
        raise HTTPException(status_code=404, detail="Deployment not found")
    
    # Add this ownership check
    if db_deployment.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this deployment")

    for key, value in deployment_update.dict(exclude_unset=True).items():
        setattr(db_deployment, key, value)

    db.add(db_deployment)
    db.commit()
    db.refresh(db_deployment)

    # deployment_manager = DeploymentManager()
    # background_tasks.add_task(deployment_manager.update, db_deployment)

    return db_deployment


@router.get("/", response_model=list[Deployment])
async def get_deployments(db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    # Admin can see all deployments, regular users only see their own
    if current_user.is_admin:
        deployments = db.query(DeploymentModel).all()
    else:
        deployments = db.query(DeploymentModel).filter(DeploymentModel.user_id == current_user.id).all()
    return deployments

# Admin-only: Get deployments by specific user
@router.get("/user/{user_id}", response_model=list[Deployment])
async def get_deployments_by_user(
    user_id: int, 
    db: Session = Depends(get_db),
    admin_user: User = Depends(get_admin_user)
):
    deployments = db.query(DeploymentModel).filter(DeploymentModel.user_id == user_id).all()
    return deployments

@router.post("/billing/refresh", response_model=dict)
async def refresh_billing(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db), 
    current_user: User = Depends(get_current_user)
):
    """
    Manually trigger billing calculation for the current user's deployments.
    This ensures users see up-to-date billing calculations.
    """
    background_tasks.add_task(run_billing_for_user_deployment, current_user.id)
    return {"message": "Billing refresh initiated for user deployments"}

@router.post("/billing/refresh/{deployment_id}", response_model=dict)
async def refresh_billing_for_deployment(
    deployment_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db), 
    current_user: User = Depends(get_current_user)
):
    """
    Manually trigger billing calculation for a specific deployment.
    """
    # Verify deployment ownership
    db_deployment = db.query(DeploymentModel).filter(DeploymentModel.id == deployment_id).first()
    if not db_deployment:
        raise HTTPException(status_code=404, detail="Deployment not found")
    
    if not current_user.is_admin and db_deployment.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this deployment")
    
    background_tasks.add_task(run_billing_for_user_deployment, current_user.id, deployment_id)
    return {"message": f"Billing refresh initiated for deployment {deployment_id}"}
