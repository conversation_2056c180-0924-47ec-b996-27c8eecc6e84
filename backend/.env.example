GCP_PROJECT_ID=your_gcp_project_id
DATABASE_URL=postgresql://your_db_user:your_db_password@localhost:5432/your_db_name
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=your_db_name
KOMODO_PROVIDER_ENDPOINT=http://your_komodo_provider_endpoint:9120
KOMODO_API_KEY=your_komodo_api_key
KOMODO_API_SECRET=your_komodo_api_secret
KOMODO_PASSKEY=your_komodo_passkey
GITHUB_TOKEN=your_github_token
VITE_API_SECRET_KEY=your_very_long_random_string

HCLOUD_TOKEN=your_hcloud_token
HETZNER_SSH_FILE=your_hetzner_ssh_file

GCP_CREDENTIALS_FILE=your_gcp_credentials_file
GCP_SSH_FILE=your_gcp_ssh_file

LINODE_TOKEN=your_linode_api_token
LINODE_SSH_FILE=your_linode_ssh_file
LINODE_ROOT_PASSWORD=your_root_password_here

VULTR_API_KEY=your_vultr_api_key
VULTR_SSH_FILE=your_vultr_ssh_file

# Feature flags
# When set to 'true', allows reusing an existing server or BYOVPS deployments.
# Default is 'false' which disables reuse for safety.
ALLOW_VPS_REUSE=false

DIGITALOCEAN_TOKEN=your_digitalocean_token
DIGITALOCEAN_SSH_FILE=your_digitalocean_ssh_file

AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=eu-west-1
AWSLIGHTSAIL_SSH_FILE=your_awslightsail_ssh_file

AZURE_CLIENT_ID=your_azure_client_id
AZURE_CLIENT_SECRET=your_azure_client_secret
AZURE_TENANT_ID=your_azure_tenant_id
AZURE_SSH_FILE=your_azure_ssh_file
AZURE_SUBSCRIPTION_ID=your_azure_subscription_id

# Feature flags
TERRAFORM_DRY_RUN=true
# When set to 'true', allows reusing an existing server or BYOVPS deployments.
# Default is 'false' which disables reuse for safety.
ALLOW_VPS_REUSE=false

# Billing configuration
MIN_BALANCE_TO_DEPLOY=4.99
BILLING_INTERVAL_MINUTES=60
BILLING_BYOVPS_DAILY_FEE=0.12

# Stripe (optional)
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=
