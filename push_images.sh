#!/bin/bash
set -e

# Your Docker Hub username or private registry URL
# Replace "your_dockerhub_username" with your actual username
DOCKER_REGISTRY="oideibrett"

echo "Pushing images to registry: ${DOCKER_REGISTRY}"
echo "You may need to 'docker login' first."

# Push images
docker push ${DOCKER_REGISTRY}/manidae-cloud-backend:latest
docker push ${DOCKER_REGISTRY}/manidae-cloud-frontend:latest

echo "Push complete."
