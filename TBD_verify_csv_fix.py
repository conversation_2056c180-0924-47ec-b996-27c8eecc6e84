#!/usr/bin/env python3
"""
Verify that the CSV file has been fixed correctly
"""
import csv
from pathlib import Path

def verify_csv_structure():
    """Verify the CSV file has correct structure"""
    print("🧪 Verifying CSV file structure...\n")
    
    csv_path = Path("backend/config/pricing.csv")
    
    if not csv_path.exists():
        print("❌ CSV file not found")
        return False
    
    try:
        with open(csv_path, 'r') as f:
            reader = csv.DictReader(f)
            header = reader.fieldnames
            
            print(f"Header columns ({len(header)}):")
            for i, col in enumerate(header, 1):
                print(f"  {i}. {col}")
            
            # Check required columns
            required_columns = [
                'cloud_provider', 'instance_type', 'region', 'hourly_cost',
                'package_multiplier_pangolin', 'package_multiplier_pangolin+', 
                'package_multiplier_pangolin+AI',
                'support_cost_level1', 'support_cost_level2', 'support_cost_level3'
            ]
            
            missing_columns = [col for col in required_columns if col not in header]
            if missing_columns:
                print(f"\n❌ Missing columns: {missing_columns}")
                return False
            
            print(f"\n✅ All required columns present")
            
            # Check first few data rows
            print(f"\nChecking data rows...")
            for i, row in enumerate(reader):
                if i >= 3:  # Check first 3 rows
                    break
                
                print(f"\nRow {i+1}:")
                print(f"  Provider: {row['cloud_provider']}")
                print(f"  Instance: {row['instance_type']}")
                print(f"  Pangolin multiplier: {row['package_multiplier_pangolin']}")
                print(f"  Pangolin+ multiplier: {row['package_multiplier_pangolin+']}")
                print(f"  Pangolin+AI multiplier: {row['package_multiplier_pangolin+AI']}")
                print(f"  Support Level 1: {row['support_cost_level1']}")
                print(f"  Support Level 2: {row['support_cost_level2']}")
                print(f"  Support Level 3: {row['support_cost_level3']}")
                
                # Check for None values
                none_values = [k for k, v in row.items() if v is None or v == '']
                if none_values:
                    print(f"  ⚠️  Empty/None values in: {none_values}")
                else:
                    print(f"  ✅ All values present")
            
            print(f"\n🎉 CSV file structure is correct!")
            return True
            
    except Exception as e:
        print(f"❌ Error reading CSV: {e}")
        return False

def test_support_cost_parsing():
    """Test that support costs can be parsed correctly"""
    print("\n🧪 Testing support cost parsing...\n")
    
    csv_path = Path("backend/config/pricing.csv")
    
    try:
        with open(csv_path, 'r') as f:
            reader = csv.DictReader(f)
            first_row = next(reader)
            
            # Test parsing support costs
            for level in [1, 2, 3]:
                key = f"support_cost_level{level}"
                value = first_row.get(key)
                
                print(f"Support Level {level}:")
                print(f"  Raw value: '{value}' (type: {type(value)})")
                
                try:
                    if value is not None and value != '':
                        float_value = float(value)
                        print(f"  Parsed as float: {float_value}")
                        print(f"  ✅ Success")
                    else:
                        print(f"  ❌ Value is None or empty")
                        return False
                except ValueError as e:
                    print(f"  ❌ Cannot parse as float: {e}")
                    return False
                
                print()
            
            print("🎉 All support costs can be parsed correctly!")
            return True
            
    except Exception as e:
        print(f"❌ Error testing support cost parsing: {e}")
        return False

def main():
    """Run all verification tests"""
    print("🔍 Verifying Pricing CSV Fix\n")
    
    csv_ok = verify_csv_structure()
    parsing_ok = test_support_cost_parsing()
    
    print("\n📊 Verification Results:")
    print(f"CSV Structure: {'✅ PASS' if csv_ok else '❌ FAIL'}")
    print(f"Support Cost Parsing: {'✅ PASS' if parsing_ok else '❌ FAIL'}")
    
    if csv_ok and parsing_ok:
        print("\n🎉 CSV file is fixed and ready!")
        print("The pricing calculation error should now be resolved.")
        return True
    else:
        print("\n❌ Issues found. Please review the CSV file.")
        return False

if __name__ == "__main__":
    main()
