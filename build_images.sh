#!/bin/bash
set -e

# Your Docker Hub username or private registry URL
# Replace "your_dockerhub_username" with your actual username
DOCKER_REGISTRY="oideibrett"

echo "Building Docker images..."

# Build and tag images using docker-compose
docker compose build

echo "Tagging images for registry: ${DOCKER_REGISTRY}"
docker tag manidae-cloud-backend:latest ${DOCKER_REGISTRY}/manidae-cloud-backend:latest
docker tag manidae-cloud-frontend:latest ${DOCKER_REGISTRY}/manidae-cloud-frontend:latest

echo "Build complete."
echo "To push them, run './push_images.sh' after logging in with 'docker login'."
