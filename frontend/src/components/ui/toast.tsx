import React from 'react';

export type ToastType = 'success' | 'error' | 'info';

export const toast = (message: string, type: ToastType = 'info') => {
  // Minimal placeholder toast using alert; replace with shadcn/toast if desired
  if (type === 'error') {
    console.error(message);
  } else {
    console.log(message);
  }
  try {
    // Non-blocking: avoid alert for better UX in real app
    // alert(`${type.toUpperCase()}: ${message}`);
  } catch {}
};

