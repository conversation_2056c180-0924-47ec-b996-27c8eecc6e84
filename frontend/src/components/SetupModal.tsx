import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts';
import { apiClient } from '../api';
import { Button } from './ui/button';
import Modal from './ui/modal';

const SetupModal: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const [showSetupModal, setShowSetupModal] = useState(false);
  const [isCheckingAdmins, setIsCheckingAdmins] = useState(false);
  const [hasChecked, setHasChecked] = useState(false);

  const checkForAdmins = async () => {
    if (!isAuthenticated || !user || hasChecked) return;
    
    // Don't show if setup has already been acknowledged
    const setupShown = localStorage.getItem('setupModalShown');
    if (setupShown === 'true') {
      setHasChecked(true);
      return;
    }
    
    setIsCheckingAdmins(true);
    try {
      // Try to get users list (only admins can do this)
      const users = await apiClient.getUsers();
      const adminCount = users.filter(u => u.is_admin).length;
      const totalUsers = users.length;
      
      // Show setup modal if:
      // 1. This user is an admin AND (one of the following):
      //    a. They are the only admin
      //    b. They are the first user (ID 1 or similar)
      //    c. Total user count is 1 (first user ever)
      if (user.is_admin && (adminCount === 1 || totalUsers === 1 || user.id <= 2)) {
        setShowSetupModal(true);
      }
    } catch (error) {
      // If user can't access users list, they're not admin
      // But if they somehow got here and there are no admins, this is a critical issue
      console.log('Cannot access user list - not an admin or system issue');
    } finally {
      setIsCheckingAdmins(false);
      setHasChecked(true);
    }
  };

  useEffect(() => {
    if (isAuthenticated && user) {
      checkForAdmins();
    }
  }, [isAuthenticated, user, hasChecked]);

  const handleCloseSetup = () => {
    setShowSetupModal(false);
    // Store in localStorage that setup has been acknowledged
    localStorage.setItem('setupModalShown', 'true');
  };

  // Don't show if we've already shown this modal
  useEffect(() => {
    const setupShown = localStorage.getItem('setupModalShown');
    if (setupShown === 'true') {
      setHasChecked(true);
    }
  }, []);

  if (!showSetupModal || isCheckingAdmins) {
    return null;
  }

  return (
    <Modal
      open={showSetupModal}
      onClose={handleCloseSetup}
      title="🎉 Welcome, Administrator!"
    >
      <div className="space-y-4">
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <h3 className="font-semibold text-blue-800 mb-2">
            System Setup Complete
          </h3>
          <p className="text-sm text-blue-700">
            Congratulations! You are now the first administrator of this Manidae Cloud instance.
          </p>
        </div>

        <div className="space-y-3">
          <h4 className="font-medium text-foreground">As an administrator, you can:</h4>
          <ul className="text-sm text-muted-foreground space-y-1 ml-4">
            <li>• View and manage all deployments from all users</li>
            <li>• Create, delete, and manage user accounts</li>
            <li>• Promote other users to administrator status</li>
            <li>• Access the user management interface</li>
          </ul>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <h4 className="font-medium text-yellow-800 mb-2">
            ⚠️ Important: Database Initialization
          </h4>
          <p className="text-sm text-yellow-700 mb-2">
            If you haven't already, make sure to run the database initialization script on your server:
          </p>
          <code className="text-xs bg-gray-800 text-green-400 px-2 py-1 rounded block">
            python -m app.db.init_db
          </code>
          <p className="text-xs text-yellow-600 mt-2">
            This ensures all database tables are created and pricing data is loaded.
          </p>
        </div>

        <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
          <h4 className="font-medium text-gray-800 mb-2">Next Steps:</h4>
          <ol className="text-sm text-gray-600 space-y-1 ml-4">
            <li>1. Navigate to "Users" in the top menu to manage users</li>
            <li>2. Create additional user accounts as needed</li>
            <li>3. Start creating your first deployment!</li>
          </ol>
        </div>

        <div className="flex justify-end">
          <Button onClick={handleCloseSetup} className="bg-primary hover:bg-primary/90">
            Got it, let's get started!
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default SetupModal;