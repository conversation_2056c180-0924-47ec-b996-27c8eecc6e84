import React, { useEffect, useState } from 'react';
import { apiClient } from '../api';
import { useAuth } from '../contexts';
import { Button } from '../components/ui/button';

interface Transaction {
  id: number;
  user_id: number;
  amount: number;
  type: 'billing' | 'manual_adjustment' | 'payment' | 'termination' | string;
  description?: string;
  created_at: string;
}

interface GroupedTransaction {
  date: string;
  transactions: Transaction[];
}

const Billing: React.FC = () => {
  const { user, refreshUser } = useAuth();
  const [groupedTransactions, setGroupedTransactions] = useState<GroupedTransaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [topUpAmount, setTopUpAmount] = useState<number>(10);

  useEffect(() => {
    const load = async () => {
      try {
        setLoading(true);
        const txs = await apiClient.getTransactions();
        console.log('Raw API response:', txs);
        
        // The API returns a single grouped object, so convert to array format
        const groupedArray = Array.isArray(txs) ? txs : [txs];
        console.log('Final grouped array:', groupedArray);
        setGroupedTransactions(groupedArray as unknown as GroupedTransaction[]);
        await refreshUser();
      } catch (e: any) {
        setError(e?.message || 'Failed to load transactions');
      } finally {
        setLoading(false);
      }
    };
    load();
  }, []);

  const startStripeTopUp = async () => {
    try {
      setError(null);
      const amountCents = Math.round(topUpAmount * 100);
      const session = await apiClient.createStripeCheckoutSession(amountCents);
      if (session?.url) {
        window.location.href = session.url;
      }
    } catch (e: any) {
      setError(e?.message || 'Failed to start Stripe checkout');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(undefined, {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <div className="max-w-3xl mx-auto space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold">Billing</h1>
        <div className="text-sm text-muted-foreground">
          Current balance: <span className="font-medium">{user?.balance != null ? `${parseFloat(String(user.balance)).toFixed(2)} EUR` : '—'}</span>
        </div>
      </div>

      <div className="rounded border p-4 space-y-3">
        <h2 className="font-medium">Add funds</h2>
        <div className="flex items-center gap-3">
          <input
            type="number"
            min={1}
            step={1}
            value={topUpAmount}
            onChange={(e) => setTopUpAmount(parseFloat(e.target.value))}
            className="w-32 rounded border bg-background px-2 py-1"
          />
          <span>EUR</span>
          <Button onClick={startStripeTopUp}>Add funds with Stripe</Button>
        </div>
        <p className="text-xs text-muted-foreground">You will be redirected to Stripe to complete the payment.</p>
        {error && <div className="text-sm text-red-600">{error}</div>}
      </div>

      <div className="rounded border p-4">
        <h2 className="font-medium mb-3">Transactions</h2>
        {loading ? (
          <div className="text-sm text-muted-foreground">Loading…</div>
        ) : (
          <div className="space-y-4">
            {groupedTransactions.length === 0 ? (
              <div className="text-sm text-muted-foreground">No transactions yet.</div>
            ) : (
              groupedTransactions.map((group) => (
                <div key={group.date}>
                  <h3 className="text-sm font-semibold text-muted-foreground mb-2 border-b pb-2">{formatDate(group.date)}</h3>
                  <div className="space-y-2">
                    {group.transactions.map((tx) => (
                      <div key={tx.id} className="flex items-center justify-between text-sm ml-2">
                        <div>
                          <div className="font-medium capitalize">{(tx.type || 'unknown').replace('_', ' ')}</div>
                          <div className="text-muted-foreground text-xs">{tx.description || ''}</div>
                        </div>
                        <div className="text-right">
                           <div className={`font-medium ${parseFloat(String(tx.amount)) >= 0 ? 'text-green-600' : ''}`}>
                            {parseFloat(String(tx.amount)) >= 0 ? '+' : ''}{parseFloat(String(tx.amount)).toFixed(2)} EUR
                          </div>
                          <div className="text-muted-foreground text-xs">{new Date(tx.created_at).toLocaleTimeString()}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default Billing;

