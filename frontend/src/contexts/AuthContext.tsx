import React, { createContext, useContext, useReducer, ReactNode, useEffect, useCallback } from 'react';
import { User, UserCreate } from '../api/types';
import { apiClient } from '../api';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

type AuthAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_USER'; payload: User }
  | { type: 'SET_ERROR'; payload: string }
  | { type: 'CLEAR_ERROR' }
  | { type: 'LOGOUT' };

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_USER':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        isLoading: false,
      };
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        error: null,
      };
    default:
      return state;
  }
};

interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<void>;
  signup: (userData: UserCreate) => Promise<void>;
  logout: () => void;
  clearError: () => void;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Load user from localStorage on app start
  useEffect(() => {
    const savedUser = localStorage.getItem('user');
    if (savedUser) {
      try {
        const user = JSON.parse(savedUser);
        dispatch({ type: 'SET_USER', payload: user });
      } catch (error) {
        localStorage.removeItem('user');
      }
    }
  }, []);

  const login = async (email: string, password: string): Promise<void> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'CLEAR_ERROR' });

    try {
      // Try proper authentication API first
      try {
        const authResponse = await apiClient.login(email, password);

        // Store the access token
        localStorage.setItem('token', authResponse.access_token);

        // Get user info using the token
        try {
          const user = await apiClient.getCurrentUser();
          dispatch({ type: 'SET_USER', payload: user });
          localStorage.setItem('user', JSON.stringify(user));
        } catch (userError) {
          // If we can't get user info, create a basic user object from email
          const basicUser: User = {
            id: 0, // We don't have the real ID
            username: email.split('@')[0], // Use part before @ as username
            email: email,
            is_admin: false
          };
          dispatch({ type: 'SET_USER', payload: basicUser });
          localStorage.setItem('user', JSON.stringify(basicUser));
        }

      } catch (apiError) {
        console.warn('Authentication API not available, checking mock users');
        // Fall back to mock users stored in localStorage
        const mockUsers = JSON.parse(localStorage.getItem('mockUsers') || '[]');
        const user = mockUsers.find((u: User) => u.email === email || u.username === email);

        if (!user) {
          throw new Error('Invalid email or password');
        }

        // Mock password validation
        if (password.length < 6) {
          throw new Error('Invalid email or password');
        }

        dispatch({ type: 'SET_USER', payload: user });
        localStorage.setItem('user', JSON.stringify(user));
      }

    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: error instanceof Error ? error.message : 'Login failed'
      });
      throw error;
    }
  };

  const signup = async (userData: UserCreate): Promise<void> => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'CLEAR_ERROR' });

    try {
      await apiClient.createUser(userData);
      // After successful signup, automatically log in the user
      await login(userData.email, userData.password);
    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: error instanceof Error ? error.message : 'Signup failed'
      });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const logout = (): void => {
    // Try to call logout API if available
    try {
      apiClient.logout().catch(() => {
        // Ignore logout API errors
      });
    } catch (error) {
      // Ignore logout API errors
    }

    dispatch({ type: 'LOGOUT' });
    localStorage.removeItem('user');
    localStorage.removeItem('token');
  };

  const clearError = useCallback((): void => {
    dispatch({ type: 'CLEAR_ERROR' });
  }, []);

  const refreshUser = async (): Promise<void> => {
    try {
      const fresh = await apiClient.getCurrentUser();
      dispatch({ type: 'SET_USER', payload: fresh });
      localStorage.setItem('user', JSON.stringify(fresh));
    } catch (e) {
      // ignore
    }
  };

  const value: AuthContextType = {
    ...state,
    login,
    signup,
    logout,
    clearError,
    refreshUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};